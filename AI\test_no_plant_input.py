#!/usr/bin/env python3
"""
Test script for the enhanced API without plant name input requirement
Tests that the API works with just image upload
"""

import requests
import json
from PIL import Image
import io
import numpy as np

# API endpoint
API_URL = "http://127.0.0.1:5006/detect-disease"

def create_test_image():
    """Create a simple test image"""
    # Create a simple green leaf-like image
    img = Image.new('RGB', (224, 224), color='green')
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_image_only_detection():
    """Test that the API works with just image upload (no plant name)"""
    print("🧪 Testing Enhanced API - Image Only Detection")
    print("=" * 60)
    
    try:
        # Create a test image
        test_image = create_test_image()
        
        # Test with only image (no plant name)
        files = {'image': ('test_leaf.jpg', test_image, 'image/jpeg')}
        
        print("📤 Sending request with image only (no plant name)...")
        response = requests.post(API_URL, files=files)
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS: API works without plant name input!")
            print(f"🔍 Detection Result:")
            print(f"   • Confirmation: {result.get('confirmation', 'N/A')}")
            print(f"   • Plant: {result.get('plant', 'N/A')}")
            print(f"   • Healthy: {result.get('healthy', 'N/A')}")
            print(f"   • Disease: {result.get('disease', 'N/A')}")
            print(f"   • Confidence: {result.get('confidence', 'N/A'):.2%}")
            print(f"   • Message: {result.get('message', 'N/A')}")
            if result.get('advice'):
                print(f"   • Advice: {result.get('advice')[:100]}...")
            
            return True
        else:
            print(f"❌ ERROR: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error message: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"🔌 Cannot connect to API. Make sure the server is running on {API_URL}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_missing_image():
    """Test that the API properly handles missing image"""
    print("\n🧪 Testing Error Handling - Missing Image")
    print("-" * 40)
    
    try:
        # Test with no image
        response = requests.post(API_URL, data={})
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 400:
            result = response.json()
            print("✅ SUCCESS: API properly rejects requests without image!")
            print(f"   Error message: {result.get('error', 'N/A')}")
            return True
        else:
            print(f"❌ UNEXPECTED: Expected 400 status code, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def display_api_changes():
    """Display the changes made to the API"""
    print("\n🔄 API Enhancement Summary")
    print("=" * 60)
    
    print("📋 Changes Made:")
    changes = [
        "✅ Removed plant name input requirement",
        "✅ API now works with image upload only", 
        "✅ Auto-detects plant type from image",
        "✅ Returns plant type in response",
        "✅ Maintains all disease detection functionality",
        "✅ Provides comprehensive advice for detected diseases"
    ]
    
    for change in changes:
        print(f"   {change}")
    
    print("\n📡 New API Usage:")
    print("   • Endpoint: POST /detect-disease")
    print("   • Required: image file only")
    print("   • Optional: none")
    print("   • Response: plant, disease, advice, confidence")
    
    print("\n🎯 Benefits:")
    benefits = [
        "🚀 Simpler user experience - just upload image",
        "🤖 Automatic plant identification", 
        "⚡ Faster workflow - no manual plant selection",
        "🎯 More accurate - model determines plant type",
        "📱 Better mobile experience"
    ]
    
    for benefit in benefits:
        print(f"   {benefit}")

if __name__ == "__main__":
    print("🚀 Testing Enhanced Crop Disease Detection API")
    print("Testing the new image-only detection functionality\n")
    
    # Test main functionality
    success1 = test_image_only_detection()
    
    # Test error handling
    success2 = test_missing_image()
    
    # Display changes
    display_api_changes()
    
    if success1 and success2:
        print("\n🎉 All Tests Passed!")
        print("   The API successfully works without plant name input!")
        print("   Users can now simply upload an image for disease detection.")
    else:
        print("\n⚠️  Some tests failed. Please check the API implementation.")
    
    print("\n" + "=" * 60)
    print("Test completed! 🌱")
