<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroMind - Slidesgo IoT Agriculture Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            color: white;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 40px;
            position: absolute;
            top: 0;
            left: 0;
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide h1 {
            font-size: 3.5em;
            margin-bottom: 30px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .slide h2 {
            font-size: 2.2em;
            margin-bottom: 20px;
            color: #FFC107;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .slide p,
        .slide li {
            font-size: 1.3em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .title-slide {
            background: linear-gradient(rgba(46, 125, 50, 0.8), rgba(76, 175, 80, 0.8)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%234CAF50" width="1200" height="800"/><circle fill="%232E7D32" cx="300" cy="200" r="120" opacity="0.4"/><circle fill="%23FFC107" cx="900" cy="600" r="180" opacity="0.3"/><rect fill="%23388E3C" x="100" y="500" width="300" height="150" opacity="0.3"/></svg>');
            background-size: cover;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .content-slide {
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            justify-content: flex-start;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 20px 0;
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .feature-box {
            background: rgba(255, 255, 255, 0.15);
            padding: 25px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-box h3 {
            color: #FFC107;
            font-size: 1.5em;
            margin-bottom: 15px;
        }

        .stats-highlight {
            background: rgba(255, 193, 7, 0.2);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 15px 0;
        }

        .stats-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #FFC107;
            display: block;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        th,
        td {
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        th {
            background: rgba(255, 193, 7, 0.3);
            font-weight: bold;
        }

        .agromind-col {
            background: rgba(255, 193, 7, 0.2);
            font-weight: bold;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        ul {
            list-style: none;
            padding-left: 0;
        }

        li::before {
            content: "🌱 ";
            margin-right: 10px;
        }

        .problem-list li::before {
            content: "⚠️ ";
        }

        .solution-list li::before {
            content: "✅ ";
        }

        .tech-list li::before {
            content: "🔧 ";
        }
    </style>
</head>

<body>
    <div class="presentation-container">
        <!-- Slide 1: Title -->
        <div class="slide active title-slide">
            <h1>🌱 AGROMIND 🤖</h1>
            <h2>Smart Agricultural Management System</h2>
            <p style="font-size: 1.8em; color: #FFC107; margin: 30px 0;">Empowering Egyptian Farmers Through AI
                Technology</p>
            <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 40px;">
                <p><strong>[Your Team Names]</strong></p>
                <p><strong>[University] | [Date]</strong></p>
            </div>
        </div>

        <!-- Slide 2: Introduction -->
        <div class="slide content-slide">
            <h1>🌍 Revolutionizing Egyptian Agriculture</h1>
            <div class="three-column">
                <div class="feature-box">
                    <h3>🌱 Expert Dashboard</h3>
                    <ul>
                        <li>Agricultural specialists management</li>
                        <li>Crop data and best practices</li>
                        <li>Visual aids and tools database</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>🤖 AI Technology</h3>
                    <ul>
                        <li>95.4% accurate disease detection</li>
                        <li>87,000+ image dataset</li>
                        <li>Mobile-optimized MobileNetV2</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>🛒 E-commerce Platform</h3>
                    <ul>
                        <li>Integrated marketplace</li>
                        <li>Agricultural products & tools</li>
                        <li>Direct farmer-to-market access</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 3: Problem Definition -->
        <div class="slide content-slide">
            <h1>⚠️ Current Challenges in Egyptian Agriculture</h1>
            <div class="two-column">
                <div>
                    <h2>Key Problems:</h2>
                    <ul class="problem-list">
                        <li><strong>Limited Expert Access</strong> - Small-scale farmers lack agricultural specialists
                        </li>
                        <li><strong>Disease Identification</strong> - Difficulty in early crop disease detection</li>
                        <li><strong>Fragmented Information</strong> - Agricultural knowledge scattered across sources
                        </li>
                        <li><strong>Market Access Issues</strong> - Limited platforms for agricultural commerce</li>
                        <li><strong>Technology Gap</strong> - Traditional methods vs. modern digital solutions</li>
                        <li><strong>Economic Impact</strong> - Crop losses due to poor disease management</li>
                    </ul>
                </div>
                <div>
                    <div class="stats-highlight">
                        <span class="stats-number">25%</span>
                        <p>Workforce in Agriculture</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">11%</span>
                        <p>GDP Contribution</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">95%</span>
                        <p>Farmers Own Smartphones</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Need for Smart Solutions -->
        <div class="slide content-slide">
            <h1>🎯 Why Smart Agricultural Management is Essential</h1>
            <div class="two-column">
                <div>
                    <h2>Statistics & Facts:</h2>
                    <ul class="solution-list">
                        <li><strong>Egypt's Agriculture:</strong> 25% workforce, 11% GDP contribution</li>
                        <li><strong>Food Security:</strong> Growing population needs increased productivity</li>
                        <li><strong>AI Revolution:</strong> Technology can transform crop management</li>
                        <li><strong>Mobile Adoption:</strong> 95% of Egyptian farmers own smartphones</li>
                        <li><strong>Precision Agriculture:</strong> Data-driven decisions improve outcomes</li>
                        <li><strong>Knowledge Transfer:</strong> Bridge experts and farmers effectively</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <div style="background: rgba(255,255,255,0.1); padding: 40px; border-radius: 20px;">
                        <h2 style="color: #FFC107;">🌍 Global Impact</h2>
                        <p style="font-size: 1.5em;">Technology adoption in agriculture can increase yields by <strong
                                style="color: #FFC107;">20-30%</strong></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: FarmLogs Example -->
        <div class="slide content-slide">
            <h1>📊 Current Agricultural Management Systems</h1>
            <h2>FarmLogs - Field Management Platform</h2>
            <div class="two-column">
                <div class="feature-box">
                    <h3>✅ Strengths:</h3>
                    <ul class="solution-list">
                        <li>Real-time weather forecasting</li>
                        <li>Field activity tracking</li>
                        <li>Crop health monitoring</li>
                        <li>Simple, user-friendly interface</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>❌ Limitations:</h3>
                    <ul class="problem-list">
                        <li>No AI disease detection</li>
                        <li>No e-commerce integration</li>
                        <li>Limited expert consultation</li>
                        <li>US-focused, not Egyptian agriculture</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 6: Competitive Analysis -->
        <div class="slide content-slide">
            <h1>🏆 How AgroMind Stands Out</h1>
            <table>
                <tr>
                    <th>Feature</th>
                    <th>FarmLogs</th>
                    <th>Granular</th>
                    <th>AgriWebb</th>
                    <th class="agromind-col">AgroMind</th>
                </tr>
                <tr>
                    <td>AI Disease Detection</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td class="agromind-col">✅ 95.4%</td>
                </tr>
                <tr>
                    <td>Expert Dashboard</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td class="agromind-col">✅ Complete</td>
                </tr>
                <tr>
                    <td>E-commerce Platform</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td class="agromind-col">✅ Integrated</td>
                </tr>
                <tr>
                    <td>Arabic Support</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td class="agromind-col">✅ Bilingual</td>
                </tr>
                <tr>
                    <td>Egyptian Crops</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td class="agromind-col">✅ 14 Species</td>
                </tr>
                <tr>
                    <td>Mobile Optimized</td>
                    <td>✅</td>
                    <td>❌</td>
                    <td>✅</td>
                    <td class="agromind-col">✅ MobileNetV2</td>
                </tr>
            </table>
        </div>

        <!-- Slide 7: Solution Overview -->
        <div class="slide content-slide">
            <h1>🎯 Our Comprehensive Solution</h1>
            <div style="text-align: center; margin-bottom: 30px;">
                <h2>Three-Pillar Architecture:</h2>
            </div>
            <div class="three-column">
                <div class="feature-box">
                    <h3>🎯 Expert Dashboard</h3>
                    <p>Agricultural specialists manage crop data and best practices</p>
                </div>
                <div class="feature-box">
                    <h3>👨‍🌾 Farmer Dashboard</h3>
                    <p>Personalized recommendations and visual guides</p>
                </div>
                <div class="feature-box">
                    <h3>🛒 E-commerce Platform</h3>
                    <p>Integrated marketplace for products</p>
                </div>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px; margin-top: 30px;">
                <h2>🤖 AI-Powered Features:</h2>
                <div class="two-column">
                    <ul class="solution-list">
                        <li>Crop disease detection (95.4% accuracy)</li>
                        <li>Intelligent chatbot support</li>
                    </ul>
                    <ul class="solution-list">
                        <li>Personalized crop recommendations</li>
                        <li>Arabic language support</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 8: Expert Dashboard -->
        <div class="slide content-slide">
            <h1>👨‍🌾 Empowering Agricultural Specialists</h1>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>📊 Crop Data Management</h3>
                        <ul class="solution-list">
                            <li>14 major crop species supported</li>
                            <li>Complete growth stage definitions</li>
                            <li>Visual aids and identification tools</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>🛠️ Tools & Resources Database</h3>
                        <ul class="solution-list">
                            <li>Agricultural equipment catalog</li>
                            <li>Fertilizer recommendations</li>
                            <li>Best practices documentation</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="feature-box">
                        <h3>👥 Expert Profile System</h3>
                        <ul class="solution-list">
                            <li>Specialization tracking</li>
                            <li>Experience management</li>
                            <li>Regional coverage mapping</li>
                            <li>Availability scheduling</li>
                        </ul>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">14+</span>
                        <p>Crop Species Supported</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: AI Disease Detection -->
        <div class="slide content-slide">
            <h1>🤖 Revolutionary AI-Powered Crop Disease Detection</h1>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>🔬 Technical Specifications</h3>
                        <ul class="tech-list">
                            <li><strong>Model:</strong> MobileNetV2 optimized</li>
                            <li><strong>Dataset:</strong> 87,000+ images</li>
                            <li><strong>Accuracy:</strong> 95.4%</li>
                            <li><strong>Speed:</strong> 2-3 seconds</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>🇪🇬 Egyptian Agriculture Focus</h3>
                        <ul class="solution-list">
                            <li>14 crop species including tomatoes, corn, citrus</li>
                            <li>Arabic language support: طماطم، برتقال، فلفل</li>
                            <li>Local treatment recommendations</li>
                        </ul>
                    </div>
                </div>
                <div style="text-align: center;">
                    <div class="stats-highlight">
                        <span class="stats-number">95.4%</span>
                        <p>Overall Accuracy</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">38</span>
                        <p>Disease Categories</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">2-3s</span>
                        <p>Inference Time</p>
                    </div>
                    <div
                        style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin-top: 20px;">
                        <h3>Process Flow:</h3>
                        <p>📱 Upload Image → 🤖 AI Analysis → 🔍 Disease ID → 💊 Treatment</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 10: E-commerce Integration -->
        <div class="slide content-slide">
            <h1>🛒 Integrated Agricultural Marketplace</h1>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>🌱 Product Categories</h3>
                        <ul class="solution-list">
                            <li>Seeds and planting materials</li>
                            <li>Agricultural tools and equipment</li>
                            <li>Fertilizers and pesticides</li>
                            <li>Fresh produce and crops</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>💳 E-commerce Features</h3>
                        <ul class="solution-list">
                            <li>Secure payment processing</li>
                            <li>Product reviews and ratings</li>
                            <li>Seller verification system</li>
                            <li>Order tracking and management</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="feature-box">
                        <h3>🔗 Smart Integration</h3>
                        <ul class="solution-list">
                            <li>Connected to expert recommendations</li>
                            <li>AI-suggested products based on crop needs</li>
                            <li>Direct links from disease diagnosis to treatments</li>
                            <li>Mobile-optimized shopping experience</li>
                        </ul>
                    </div>
                    <div
                        style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; text-align: center;">
                        <h3 style="color: #FFC107;">🎯 Seamless Experience</h3>
                        <p>Disease Detection → Treatment Recommendation → Purchase Solution</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 11: Case Study - AI Performance -->
        <div class="slide content-slide">
            <h1>📊 Real-World AI Performance Metrics</h1>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>🎯 Training & Validation Results</h3>
                        <ul class="tech-list">
                            <li><strong>Overall Accuracy:</strong> 95.4%</li>
                            <li><strong>Precision:</strong> 94.8% weighted average</li>
                            <li><strong>Recall:</strong> 95.1% weighted average</li>
                            <li><strong>F1-Score:</strong> 94.9% balanced performance</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>👨‍🔬 Expert Validation</h3>
                        <ul class="solution-list">
                            <li>5-expert panel validation</li>
                            <li>92% agreement with professional diagnoses</li>
                            <li>1,000 professionally diagnosed images</li>
                            <li>Confidence calibration validated</li>
                        </ul>
                    </div>
                </div>
                <div style="text-align: center;">
                    <div class="stats-highlight">
                        <span class="stats-number">95.4%</span>
                        <p>Overall Accuracy</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">92%</span>
                        <p>Expert Agreement</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">87%</span>
                        <p>Poor Lighting Accuracy</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">78%</span>
                        <p>Early Symptoms Detection</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 12: Egyptian Agriculture Impact -->
        <div class="slide content-slide">
            <h1>🇪🇬 Transforming Local Farming Practices</h1>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>📈 Coverage Expansion</h3>
                        <ul class="solution-list">
                            <li><strong>Before:</strong> 4 species, 13 disease classes</li>
                            <li><strong>After:</strong> 14 species, 38 disease classes</li>
                            <li><strong>Improvement:</strong> 250% increase</li>
                            <li><strong>Egyptian Crops:</strong> Tomatoes, corn, potatoes, citrus, grapes, wheat</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>🌍 Cultural Adaptation</h3>
                        <ul class="solution-list">
                            <li>Arabic language interface</li>
                            <li>Local agricultural practices integrated</li>
                            <li>Culturally appropriate recommendations</li>
                            <li>Egyptian farming methods supported</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="stats-highlight">
                        <span class="stats-number">15-20%</span>
                        <p>Yield Improvement</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">30%</span>
                        <p>Pesticide Cost Reduction</p>
                    </div>
                    <div class="stats-highlight">
                        <span class="stats-number">Instant</span>
                        <p>vs. Days for Expert Consultation</p>
                    </div>
                    <div
                        style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin-top: 20px;">
                        <h3 style="color: #FFC107;">🏆 Economic Benefits</h3>
                        <p>Early detection prevents crop losses through timely intervention</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Technical Architecture -->
        <div class="slide content-slide">
            <h1>🔧 Modern, Scalable Technology Stack</h1>
            <div class="three-column">
                <div class="feature-box">
                    <h3>🖥️ Frontend Layer</h3>
                    <ul class="tech-list">
                        <li>React.js framework</li>
                        <li>Bootstrap & Material-UI</li>
                        <li>Axios API communication</li>
                        <li>Mobile-responsive design</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>⚙️ Backend Layer</h3>
                    <ul class="tech-list">
                        <li>.NET Core APIs</li>
                        <li>Entity Framework Core</li>
                        <li>RESTful architecture</li>
                        <li>Secure authentication</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>🤖 AI Integration</h3>
                    <ul class="tech-list">
                        <li>Python Flask API</li>
                        <li>TensorFlow framework</li>
                        <li>MobileNetV2 optimization</li>
                        <li>Real-time processing</li>
                    </ul>
                </div>
            </div>
            <div
                style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px; margin-top: 30px; text-align: center;">
                <h2>🗄️ Data Layer: SQL Server + Image Storage + Structured Agricultural Data</h2>
            </div>
        </div>

        <!-- Slide 14: Future Enhancements -->
        <div class="slide content-slide">
            <h1>🚀 Innovation Roadmap</h1>
            <div class="three-column">
                <div class="feature-box">
                    <h3>🔮 Phase 1: Advanced AI</h3>
                    <ul class="solution-list">
                        <li>Yield forecasting models</li>
                        <li>Weather-based recommendations</li>
                        <li>Satellite imagery integration</li>
                        <li>Disease outbreak prediction</li>
                    </ul>
                    <div class="stats-highlight">
                        <span class="stats-number">Q3 2025</span>
                        <p>Timeline</p>
                    </div>
                </div>
                <div class="feature-box">
                    <h3>🌐 Phase 2: Platform Expansion</h3>
                    <ul class="solution-list">
                        <li>Mobile app development</li>
                        <li>IoT sensor integration</li>
                        <li>Blockchain supply chain</li>
                        <li>Multi-language support</li>
                    </ul>
                    <div class="stats-highlight">
                        <span class="stats-number">Q4 2025</span>
                        <p>Timeline</p>
                    </div>
                </div>
                <div class="feature-box">
                    <h3>📊 Phase 3: Analytics Suite</h3>
                    <ul class="solution-list">
                        <li>Farm performance dashboards</li>
                        <li>Market price predictions</li>
                        <li>Crop rotation optimization</li>
                        <li>Sustainability metrics</li>
                    </ul>
                    <div class="stats-highlight">
                        <span class="stats-number">2026</span>
                        <p>Timeline</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 15: Project Impact -->
        <div class="slide content-slide">
            <h1>🌍 Transforming Egyptian Agriculture</h1>
            <div class="three-column">
                <div class="feature-box">
                    <h3>👨‍🌾 For Farmers</h3>
                    <ul class="solution-list">
                        <li>Instant access to expert knowledge</li>
                        <li>Early disease detection saves crops</li>
                        <li>Personalized recommendations increase yields</li>
                        <li>Direct market access through e-commerce</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>🌱 For Agricultural Experts</h3>
                    <ul class="solution-list">
                        <li>Efficient knowledge sharing platform</li>
                        <li>Wider reach to more farmers</li>
                        <li>Data-driven insights for better advice</li>
                        <li>Professional networking opportunities</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>🇪🇬 For Egyptian Agriculture</h3>
                    <ul class="solution-list">
                        <li>Increased food security</li>
                        <li>Reduced crop losses</li>
                        <li>Technology adoption in rural areas</li>
                        <li>Economic growth in agricultural sector</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 16: Conclusion -->
        <div class="slide title-slide">
            <h1>🚀 AgroMind: The Future of Smart Agriculture</h1>
            <div class="two-column" style="margin-top: 40px;">
                <div class="feature-box">
                    <h3>🎯 Key Achievements</h3>
                    <ul class="solution-list">
                        <li>Comprehensive agricultural management platform</li>
                        <li>95.4% accurate AI disease detection</li>
                        <li>Integrated expert-farmer-marketplace ecosystem</li>
                        <li>Mobile-optimized for Egyptian agriculture</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>💡 Innovation Highlights</h3>
                    <ul class="solution-list">
                        <li>First Arabic-supported agricultural AI in Egypt</li>
                        <li>87,000+ image dataset for robust performance</li>
                        <li>Real-world tested with positive farmer feedback</li>
                        <li>Scalable architecture for future expansion</li>
                    </ul>
                </div>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 40px;">
                <h2 style="color: #FFC107; font-size: 2em;">🌱 Vision</h2>
                <p style="font-size: 1.5em;">"Empowering every Egyptian farmer with AI-driven agricultural intelligence"
                </p>
            </div>
        </div>

        <!-- Slide 17: Thank You -->
        <div class="slide title-slide">
            <h1>🙏 Thank You for Your Attention</h1>
            <div style="background: rgba(255,255,255,0.1); padding: 40px; border-radius: 15px; margin: 40px 0;">
                <h2 style="color: #FFC107;">📧 Contact Information</h2>
                <p style="font-size: 1.3em; margin: 15px 0;"><strong>Email:</strong> [Your team email]</p>
                <p style="font-size: 1.3em; margin: 15px 0;"><strong>GitHub:</strong> [Repository links]</p>
                <p style="font-size: 1.3em; margin: 15px 0;"><strong>Demo:</strong> [Live demonstration]</p>
            </div>
            <h2 style="color: #FFC107; font-size: 2.5em;">❓ Questions & Discussion</h2>
            <p style="font-size: 1.4em;">We welcome your questions and feedback about AgroMind</p>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← Previous</button>
        <button class="nav-btn" onclick="nextSlide()">Next →</button>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">17</span>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function (e) {
            if (e.key === 'ArrowRight' || e.key === ' ') nextSlide();
            if (e.key === 'ArrowLeft') previousSlide();
        });
    </script>
</body>

</html>