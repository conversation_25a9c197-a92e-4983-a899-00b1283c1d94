.container {
    padding: 20px;
  }
  
  h2 {
    color: #2c3e50;
    margin-bottom: 20px;
  }
  
  .grid {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }
  
  .card {
    background: #e8f5e9;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    width: 180px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
  }
  
  .card:hover {
    transform: scale(1.05);
  }
  
  .icon {
    font-size: 50px;
    color: #388e3c;
  }
  
  p {
    font-weight: bold;
    font-size: 16px;
  }
  
  span {
    color: #616161;
    font-size: 14px;
  }
  