<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroMind - Enhanced Professional Presentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', '<PERSON><PERSON>', 'Arial', sans-serif;
            background: linear-gradient(135deg, #1B5E20, #2E7D32, #388E3C);
            color: white;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 50px;
            position: absolute;
            top: 0;
            left: 0;
            background: linear-gradient(135deg, #1B5E20, #2E7D32, #388E3C);
            animation: slideIn 0.6s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(40px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide h1 {
            font-size: 3.2em;
            margin-bottom: 35px;
            text-align: center;
            text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
            font-weight: 300;
            letter-spacing: 1px;
        }

        .slide h2 {
            font-size: 2.4em;
            margin-bottom: 25px;
            color: #FFC107;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
            font-weight: 400;
        }

        .slide p,
        .slide li {
            font-size: 1.4em;
            line-height: 1.7;
            margin-bottom: 18px;
            font-weight: 300;
        }

        .title-slide {
            background: linear-gradient(rgba(27, 94, 32, 0.85), rgba(46, 125, 50, 0.85)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="1200" height="800" fill="%234CAF50"/><rect width="1200" height="800" fill="url(%23grid)"/><circle cx="200" cy="150" r="80" fill="%232E7D32" opacity="0.3"/><circle cx="1000" cy="650" r="120" fill="%23FFC107" opacity="0.2"/><rect x="300" y="400" width="600" height="200" rx="20" fill="%23388E3C" opacity="0.2"/></svg>');
            background-size: cover;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .content-slide {
            background: linear-gradient(135deg, #1B5E20, #2E7D32, #388E3C);
            justify-content: flex-start;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            margin: 25px 0;
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 35px;
            margin: 25px 0;
        }

        .feature-box {
            background: rgba(255, 255, 255, 0.12);
            padding: 35px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-box h3 {
            color: #FFC107;
            font-size: 1.6em;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .professional-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #FFC107, #FF9800);
            border-radius: 50%;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2em;
            font-weight: bold;
            color: white;
            box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
            position: relative;
        }

        .professional-icon::after {
            content: '';
            position: absolute;
            width: 120px;
            height: 120px;
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }

            100% {
                transform: scale(1.1);
                opacity: 0;
            }
        }

        .stats-highlight {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 152, 0, 0.15));
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            border-left: 5px solid #FFC107;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .stats-number {
            font-size: 3.2em;
            font-weight: 700;
            color: #FFC107;
            display: block;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .infographic-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 35px;
            border-radius: 20px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .infographic-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
            transition: width 1s ease-in-out;
        }

        .professional-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .professional-table th {
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            color: white;
            padding: 20px;
            font-weight: 600;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .professional-table td {
            padding: 18px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 1.1em;
        }

        .professional-table .agromind-col {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(139, 195, 74, 0.2));
            font-weight: 600;
            color: #E8F5E8;
        }

        .bullet-list {
            list-style: none;
            padding: 0;
        }

        .bullet-list li {
            margin: 18px 0;
            padding-left: 35px;
            position: relative;
            font-size: 1.2em;
            line-height: 1.6;
        }

        .bullet-list li::before {
            content: '▶';
            position: absolute;
            left: 0;
            font-size: 16px;
            color: #4CAF50;
            font-weight: bold;
        }

        .success-list li::before {
            content: '✓';
            color: #4CAF50;
            font-weight: bold;
            font-size: 18px;
            background: rgba(76, 175, 80, 0.2);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .warning-list li::before {
            content: '⚠';
            color: #FF9800;
            font-weight: bold;
            font-size: 18px;
        }

        .tech-list li::before {
            content: '●';
            color: #2196F3;
            font-weight: bold;
            font-size: 18px;
        }

        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 35px 0;
            flex-wrap: wrap;
        }

        .process-step {
            background: rgba(255, 255, 255, 0.15);
            padding: 25px;
            border-radius: 20px;
            text-align: center;
            flex: 1;
            margin: 0 10px;
            border: 2px solid rgba(255, 193, 7, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }

        .process-arrow {
            font-size: 2.5em;
            color: #FFC107;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 25px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            z-index: 1000;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .image-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #4CAF50, #8BC34A);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2em;
            font-weight: 500;
            margin: 20px 0;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .metric-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .highlight-banner {
            background: linear-gradient(135deg, #FFC107, #FF9800);
            color: #1B5E20;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
            font-size: 1.3em;
            margin: 25px 0;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }
    </style>
</head>

<body>
    <div class="presentation-container">
        <!-- Slide 1: Title -->
        <div class="slide active title-slide">
            <h1>AGROMIND</h1>
            <h2>Smart Agricultural Management System</h2>
            <p style="font-size: 1.8em; color: #FFC107; margin: 30px 0; font-weight: 400;">Empowering Egyptian Farmers
                Through Artificial Intelligence Technology</p>
            <div
                style="background: rgba(255,255,255,0.1); padding: 35px; border-radius: 20px; margin-top: 50px; backdrop-filter: blur(15px);">
                <p style="font-size: 1.3em; margin: 10px 0;"><strong>Team Members:</strong> [Your Team Names]</p>
                <p style="font-size: 1.3em; margin: 10px 0;"><strong>Supervisor:</strong> [Supervisor Name]</p>
                <p style="font-size: 1.3em; margin: 10px 0;"><strong>University:</strong> [Your University]</p>
                <p style="font-size: 1.3em; margin: 10px 0;"><strong>Date:</strong> [Presentation Date]</p>
            </div>
        </div>

        <!-- Slide 2: Introduction -->
        <div class="slide content-slide">
            <h1>System Overview</h1>
            <div class="image-placeholder">
                [AgroMind System Architecture Diagram]
            </div>
            <div class="three-column">
                <div class="feature-box">
                    <div class="professional-icon">ED</div>
                    <h3>EXPERT DASHBOARD</h3>
                    <ul class="bullet-list">
                        <li>Agricultural specialists management</li>
                        <li>Crop data and best practices</li>
                        <li>Visual aids and tools database</li>
                        <li>Regional coverage mapping</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">AI</div>
                    <h3>AI TECHNOLOGY</h3>
                    <ul class="bullet-list">
                        <li>95.4% accurate disease detection</li>
                        <li>87,000+ image dataset</li>
                        <li>Mobile-optimized MobileNetV2</li>
                        <li>Real-time processing</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">EC</div>
                    <h3>E-COMMERCE PLATFORM</h3>
                    <ul class="bullet-list">
                        <li>Integrated marketplace</li>
                        <li>Agricultural products & tools</li>
                        <li>Direct farmer-to-market access</li>
                        <li>Secure payment processing</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 3: Problem Definition -->
        <div class="slide content-slide">
            <h1>Agricultural Challenges in Egypt</h1>
            <div class="two-column">
                <div>
                    <div class="image-placeholder">
                        [Egyptian Farm Landscape Image]
                    </div>
                    <h2>Current Challenges:</h2>
                    <ul class="warning-list">
                        <li><strong>Limited Expert Access:</strong> Small-scale farmers lack agricultural specialists
                        </li>
                        <li><strong>Disease Identification:</strong> Difficulty in early crop disease detection</li>
                        <li><strong>Fragmented Information:</strong> Agricultural knowledge scattered across sources
                        </li>
                        <li><strong>Market Access Issues:</strong> Limited platforms for agricultural commerce</li>
                        <li><strong>Technology Gap:</strong> Traditional methods vs. modern digital solutions</li>
                    </ul>
                </div>
                <div>
                    <div class="chart-container">
                        <h3 style="color: #FFC107; text-align: center; margin-bottom: 20px;">Egyptian Agriculture
                            Statistics</h3>
                        <div class="stats-highlight">
                            <span class="stats-number">25%</span>
                            <p>Workforce in Agriculture</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 25%;">25% of Total Workforce</div>
                            </div>
                        </div>
                        <div class="stats-highlight">
                            <span class="stats-number">11%</span>
                            <p>GDP Contribution</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 11%;">11% of National GDP</div>
                            </div>
                        </div>
                        <div class="stats-highlight">
                            <span class="stats-number">95%</span>
                            <p>Smartphone Adoption</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 95%;">95% of Farmers Own Smartphones</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Need for Smart Solutions -->
        <div class="slide content-slide">
            <h1>The Need for Smart Agricultural Management</h1>
            <div class="two-column">
                <div>
                    <div class="image-placeholder">
                        [Technology in Agriculture Infographic]
                    </div>
                    <h2>Key Drivers:</h2>
                    <ul class="success-list">
                        <li><strong>Food Security:</strong> Growing population demands increased productivity</li>
                        <li><strong>Climate Change:</strong> Adaptation requires intelligent farming solutions</li>
                        <li><strong>Resource Optimization:</strong> Efficient use of water, fertilizers, and pesticides
                        </li>
                        <li><strong>Economic Sustainability:</strong> Maximizing farmer income and reducing costs</li>
                        <li><strong>Knowledge Transfer:</strong> Bridging the gap between experts and farmers</li>
                    </ul>
                </div>
                <div>
                    <div class="metric-card">
                        <div class="professional-icon">GI</div>
                        <h3>GLOBAL IMPACT</h3>
                        <div class="stats-highlight">
                            <span class="stats-number">20-30%</span>
                            <p>Yield Increase with Technology Adoption</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 75%;">Productivity Enhancement</div>
                            </div>
                        </div>
                    </div>
                    <div class="highlight-banner">
                        Technology-driven agriculture can increase global food production by 70% by 2050
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Existing Systems Analysis -->
        <div class="slide content-slide">
            <h1>Current Agricultural Management Systems</h1>
            <div class="image-placeholder">
                [Competitive Landscape Comparison Chart]
            </div>
            <div class="two-column">
                <div class="feature-box">
                    <h3>FarmLogs - Strengths</h3>
                    <ul class="success-list">
                        <li>Real-time weather forecasting</li>
                        <li>Field activity tracking</li>
                        <li>Crop health monitoring</li>
                        <li>User-friendly interface</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>Industry Limitations</h3>
                    <ul class="warning-list">
                        <li>No AI disease detection capabilities</li>
                        <li>Limited e-commerce integration</li>
                        <li>Lack of expert consultation features</li>
                        <li>Geographic focus on developed markets</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 6: Competitive Analysis -->
        <div class="slide content-slide">
            <h1>AgroMind Competitive Advantage</h1>
            <div class="image-placeholder">
                [Feature Comparison Matrix Visualization]
            </div>
            <table class="professional-table">
                <tr>
                    <th>Feature</th>
                    <th>FarmLogs</th>
                    <th>Granular</th>
                    <th>AgriWebb</th>
                    <th class="agromind-col">AgroMind</th>
                </tr>
                <tr>
                    <td>AI Disease Detection</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td class="agromind-col">✓ 95.4% Accuracy</td>
                </tr>
                <tr>
                    <td>Expert Dashboard</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td class="agromind-col">✓ Complete Management</td>
                </tr>
                <tr>
                    <td>E-commerce Platform</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td class="agromind-col">✓ Fully Integrated</td>
                </tr>
                <tr>
                    <td>Arabic Language Support</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td class="agromind-col">✓ Native Support</td>
                </tr>
                <tr>
                    <td>Egyptian Crop Focus</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td>✗</td>
                    <td class="agromind-col">✓ 14 Local Species</td>
                </tr>
                <tr>
                    <td>Mobile Optimization</td>
                    <td>✓</td>
                    <td>✗</td>
                    <td>✓</td>
                    <td class="agromind-col">✓ MobileNetV2</td>
                </tr>
            </table>
        </div>

        <!-- Slide 7: Solution Overview -->
        <div class="slide content-slide">
            <h1>AgroMind Comprehensive Solution</h1>
            <div class="image-placeholder">
                [System Integration Flow Diagram]
            </div>
            <div class="highlight-banner">
                Integrated Three-Pillar Architecture for Complete Agricultural Management
            </div>
            <div class="three-column">
                <div class="feature-box">
                    <div class="professional-icon">1</div>
                    <h3>EXPERT DASHBOARD</h3>
                    <p style="text-align: center; margin: 15px 0;">Agricultural specialists manage comprehensive crop
                        data and share best practices with farmers</p>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">2</div>
                    <h3>FARMER DASHBOARD</h3>
                    <p style="text-align: center; margin: 15px 0;">Personalized recommendations and AI-powered disease
                        detection for optimal crop management</p>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">3</div>
                    <h3>E-COMMERCE PLATFORM</h3>
                    <p style="text-align: center; margin: 15px 0;">Integrated marketplace connecting farmers directly
                        with suppliers and buyers</p>
                </div>
            </div>
        </div>

        <!-- Slide 8: AI Disease Detection -->
        <div class="slide content-slide">
            <h1>AI-Powered Crop Disease Detection System</h1>
            <div class="two-column">
                <div>
                    <div class="image-placeholder">
                        [AI Model Architecture Diagram]
                    </div>
                    <div class="feature-box">
                        <h3>Technical Specifications</h3>
                        <ul class="tech-list">
                            <li><strong>Architecture:</strong> MobileNetV2 optimized for mobile deployment</li>
                            <li><strong>Dataset:</strong> 87,000+ professionally labeled images</li>
                            <li><strong>Accuracy:</strong> 95.4% overall performance</li>
                            <li><strong>Processing Speed:</strong> 2-3 seconds inference time</li>
                            <li><strong>Coverage:</strong> 38 disease categories across 14 crop species</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="chart-container">
                        <h3 style="color: #FFC107; text-align: center; margin-bottom: 20px;">Performance Metrics</h3>
                        <div class="stats-highlight">
                            <span class="stats-number">95.4%</span>
                            <p>Overall Accuracy</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 95.4%;">95.4% Accuracy</div>
                            </div>
                        </div>
                        <div class="stats-highlight">
                            <span class="stats-number">92%</span>
                            <p>Expert Agreement Rate</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 92%;">Expert Validation</div>
                            </div>
                        </div>
                        <div class="stats-highlight">
                            <span class="stats-number">38</span>
                            <p>Disease Categories</p>
                        </div>
                    </div>
                    <div class="process-flow">
                        <div class="process-step">
                            <strong>Image Upload</strong>
                        </div>
                        <div class="process-arrow">→</div>
                        <div class="process-step">
                            <strong>AI Analysis</strong>
                        </div>
                        <div class="process-arrow">→</div>
                        <div class="process-step">
                            <strong>Treatment Plan</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Case Study Performance -->
        <div class="slide content-slide">
            <h1>Real-World Performance Validation</h1>
            <div class="image-placeholder">
                [Performance Metrics Dashboard Screenshot]
            </div>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>Training & Validation Results</h3>
                        <div class="metric-card">
                            <span class="stats-number">94.8%</span>
                            <p>Precision Score</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 94.8%;">Precision</div>
                            </div>
                        </div>
                        <div class="metric-card">
                            <span class="stats-number">95.1%</span>
                            <p>Recall Score</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 95.1%;">Recall</div>
                            </div>
                        </div>
                        <div class="metric-card">
                            <span class="stats-number">94.9%</span>
                            <p>F1-Score</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 94.9%;">F1-Score</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="feature-box">
                        <h3>Expert Validation Study</h3>
                        <ul class="success-list">
                            <li>Five-expert panel validation conducted</li>
                            <li>92% agreement with professional diagnoses</li>
                            <li>1,000 professionally diagnosed test images</li>
                            <li>Confidence calibration validated across conditions</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>Edge Case Performance</h3>
                        <ul class="tech-list">
                            <li>Poor lighting conditions: 87% accuracy maintained</li>
                            <li>Multiple disease presence: 82% accuracy</li>
                            <li>Early symptom detection: 78% accuracy</li>
                            <li>Field condition testing: 85% average accuracy</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 10: Egyptian Agriculture Impact -->
        <div class="slide content-slide">
            <h1>Transforming Egyptian Agricultural Practices</h1>
            <div class="image-placeholder">
                [Before/After Egyptian Farm Productivity Chart]
            </div>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>Coverage Expansion</h3>
                        <div class="stats-highlight">
                            <span class="stats-number">250%</span>
                            <p>Disease Coverage Increase</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 100%;">4 → 14 Crop Species</div>
                            </div>
                        </div>
                        <ul class="success-list">
                            <li><strong>Before:</strong> 4 species, 13 disease classes</li>
                            <li><strong>After:</strong> 14 species, 38 disease classes</li>
                            <li><strong>Focus Crops:</strong> Tomatoes, corn, potatoes, citrus, grapes, wheat</li>
                            <li><strong>Regional Adaptation:</strong> Egyptian climate and soil conditions</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="chart-container">
                        <h3 style="color: #FFC107; text-align: center; margin-bottom: 20px;">Economic Impact</h3>
                        <div class="stats-highlight">
                            <span class="stats-number">15-20%</span>
                            <p>Yield Improvement</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 75%;">Productivity Increase</div>
                            </div>
                        </div>
                        <div class="stats-highlight">
                            <span class="stats-number">30%</span>
                            <p>Pesticide Cost Reduction</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 70%;">Cost Savings</div>
                            </div>
                        </div>
                        <div class="stats-highlight">
                            <span class="stats-number">Instant</span>
                            <p>vs. Days for Expert Consultation</p>
                        </div>
                    </div>
                    <div class="feature-box">
                        <h3>Cultural Adaptation</h3>
                        <ul class="success-list">
                            <li>Native Arabic language interface</li>
                            <li>Local agricultural practices integration</li>
                            <li>Culturally appropriate treatment recommendations</li>
                            <li>Egyptian farming calendar alignment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 11: Technical Architecture -->
        <div class="slide content-slide">
            <h1>System Architecture & Technology Stack</h1>
            <div class="image-placeholder">
                [Technical Architecture Diagram with Data Flow]
            </div>
            <div class="three-column">
                <div class="feature-box">
                    <div class="professional-icon">FE</div>
                    <h3>FRONTEND LAYER</h3>
                    <ul class="tech-list">
                        <li>React.js framework for dynamic UI</li>
                        <li>Bootstrap & Material-UI components</li>
                        <li>Axios for seamless API communication</li>
                        <li>Responsive design for all devices</li>
                        <li>Progressive Web App capabilities</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">BE</div>
                    <h3>BACKEND LAYER</h3>
                    <ul class="tech-list">
                        <li>.NET Core 6.0 API framework</li>
                        <li>Entity Framework Core ORM</li>
                        <li>RESTful API architecture</li>
                        <li>JWT authentication & authorization</li>
                        <li>Microservices-ready design</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">AI</div>
                    <h3>AI INTEGRATION</h3>
                    <ul class="tech-list">
                        <li>Python Flask API for AI services</li>
                        <li>TensorFlow 2.x framework</li>
                        <li>MobileNetV2 optimized models</li>
                        <li>Real-time image processing</li>
                        <li>Scalable inference pipeline</li>
                    </ul>
                </div>
            </div>
            <div class="highlight-banner">
                Scalable, Cloud-Ready Architecture: SQL Server Database + Azure Cloud Storage + Docker Containerization
            </div>
        </div>

        <!-- Slide 12: Expert Dashboard Features -->
        <div class="slide content-slide">
            <h1>Expert Dashboard: Empowering Agricultural Specialists</h1>
            <div class="image-placeholder">
                [Expert Dashboard Interface Screenshots]
            </div>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>Crop Data Management</h3>
                        <ul class="success-list">
                            <li>Comprehensive database of 14+ crop species</li>
                            <li>Detailed growth stage definitions and timelines</li>
                            <li>Visual identification aids and reference materials</li>
                            <li>Disease symptom documentation with images</li>
                            <li>Treatment protocol management</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>Tools & Resources Database</h3>
                        <ul class="success-list">
                            <li>Agricultural equipment catalog and specifications</li>
                            <li>Fertilizer recommendations by crop and soil type</li>
                            <li>Best practices documentation library</li>
                            <li>Seasonal farming calendar integration</li>
                            <li>Research paper and publication repository</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="feature-box">
                        <h3>Expert Profile & Network</h3>
                        <ul class="success-list">
                            <li>Specialization area tracking and certification</li>
                            <li>Experience level and expertise validation</li>
                            <li>Regional coverage and availability scheduling</li>
                            <li>Performance metrics and farmer feedback</li>
                            <li>Professional networking and collaboration tools</li>
                        </ul>
                    </div>
                    <div class="chart-container">
                        <h3 style="color: #FFC107; text-align: center; margin-bottom: 20px;">System Capabilities</h3>
                        <div class="stats-highlight">
                            <span class="stats-number">14+</span>
                            <p>Crop Species Supported</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 100%;">Complete Agricultural Coverage</div>
                            </div>
                        </div>
                        <div class="stats-highlight">
                            <span class="stats-number">500+</span>
                            <p>Agricultural Tools Cataloged</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: E-commerce Integration -->
        <div class="slide content-slide">
            <h1>Integrated Agricultural Marketplace</h1>
            <div class="image-placeholder">
                [E-commerce Platform Interface and User Journey]
            </div>
            <div class="two-column">
                <div>
                    <div class="feature-box">
                        <h3>Product Categories & Services</h3>
                        <ul class="success-list">
                            <li>Premium seeds and planting materials</li>
                            <li>Professional agricultural tools and equipment</li>
                            <li>Organic and synthetic fertilizers</li>
                            <li>Pesticides and crop protection products</li>
                            <li>Fresh produce direct from farms</li>
                            <li>Agricultural consulting services</li>
                        </ul>
                    </div>
                    <div class="feature-box">
                        <h3>Advanced E-commerce Features</h3>
                        <ul class="success-list">
                            <li>Secure payment gateway integration</li>
                            <li>Comprehensive product review system</li>
                            <li>Verified seller certification program</li>
                            <li>Real-time order tracking and logistics</li>
                            <li>Bulk ordering for agricultural cooperatives</li>
                            <li>Seasonal pricing and discount management</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="feature-box">
                        <h3>Smart Integration Capabilities</h3>
                        <ul class="success-list">
                            <li>AI-powered product recommendations</li>
                            <li>Direct links from disease diagnosis to treatments</li>
                            <li>Expert-curated product selections</li>
                            <li>Mobile-optimized shopping experience</li>
                            <li>Integration with farmer dashboard workflows</li>
                            <li>Automated reordering based on crop cycles</li>
                        </ul>
                    </div>
                    <div class="process-flow">
                        <div class="process-step">
                            <strong>Disease Detection</strong>
                        </div>
                        <div class="process-arrow">→</div>
                        <div class="process-step">
                            <strong>Treatment Recommendation</strong>
                        </div>
                        <div class="process-arrow">→</div>
                        <div class="process-step">
                            <strong>Product Purchase</strong>
                        </div>
                    </div>
                    <div class="highlight-banner">
                        Seamless Integration: From Problem Identification to Solution Implementation
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 14: Future Enhancements & Roadmap -->
        <div class="slide content-slide">
            <h1>Innovation Roadmap & Future Development</h1>
            <div class="image-placeholder">
                [Development Timeline and Feature Roadmap Visualization]
            </div>
            <div class="three-column">
                <div class="feature-box">
                    <div class="professional-icon">P1</div>
                    <h3>PHASE 1: ADVANCED AI</h3>
                    <ul class="success-list">
                        <li>Predictive yield forecasting models</li>
                        <li>Weather-integrated crop recommendations</li>
                        <li>Satellite imagery analysis integration</li>
                        <li>Disease outbreak prediction algorithms</li>
                        <li>Soil health assessment through imaging</li>
                    </ul>
                    <div class="stats-highlight">
                        <span class="stats-number">Q3 2025</span>
                        <p>Target Completion</p>
                    </div>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">P2</div>
                    <h3>PHASE 2: PLATFORM EXPANSION</h3>
                    <ul class="success-list">
                        <li>Native mobile applications (iOS/Android)</li>
                        <li>IoT sensor network integration</li>
                        <li>Blockchain supply chain tracking</li>
                        <li>Multi-language support expansion</li>
                        <li>International market adaptation</li>
                    </ul>
                    <div class="stats-highlight">
                        <span class="stats-number">Q4 2025</span>
                        <p>Target Completion</p>
                    </div>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">P3</div>
                    <h3>PHASE 3: ANALYTICS & INTELLIGENCE</h3>
                    <ul class="success-list">
                        <li>Advanced farm performance dashboards</li>
                        <li>Market price prediction algorithms</li>
                        <li>Crop rotation optimization engine</li>
                        <li>Sustainability and carbon footprint metrics</li>
                        <li>AI-driven agricultural research insights</li>
                    </ul>
                    <div class="stats-highlight">
                        <span class="stats-number">2026</span>
                        <p>Target Completion</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 15: Project Impact & Benefits -->
        <div class="slide content-slide">
            <h1>Comprehensive Impact Assessment</h1>
            <div class="image-placeholder">
                [Stakeholder Impact Visualization and Benefits Matrix]
            </div>
            <div class="three-column">
                <div class="feature-box">
                    <div class="professional-icon">F</div>
                    <h3>FARMER BENEFITS</h3>
                    <ul class="success-list">
                        <li>Instant access to expert agricultural knowledge</li>
                        <li>Early disease detection prevents crop losses</li>
                        <li>Personalized recommendations increase yields</li>
                        <li>Direct market access reduces intermediary costs</li>
                        <li>Mobile-first design for field accessibility</li>
                        <li>Arabic language support for local farmers</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">E</div>
                    <h3>EXPERT ADVANTAGES</h3>
                    <ul class="success-list">
                        <li>Efficient knowledge sharing platform</li>
                        <li>Expanded reach to rural farming communities</li>
                        <li>Data-driven insights for evidence-based advice</li>
                        <li>Professional networking and collaboration</li>
                        <li>Performance tracking and impact measurement</li>
                        <li>Continuous learning from AI-assisted diagnostics</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <div class="professional-icon">EG</div>
                    <h3>NATIONAL IMPACT</h3>
                    <ul class="success-list">
                        <li>Enhanced food security and agricultural productivity</li>
                        <li>Reduced crop losses and economic waste</li>
                        <li>Technology adoption in rural communities</li>
                        <li>Economic growth in agricultural sector</li>
                        <li>Sustainable farming practice promotion</li>
                        <li>Digital transformation of agriculture</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 16: Conclusion -->
        <div class="slide title-slide">
            <h1>AgroMind: Transforming Egyptian Agriculture</h1>
            <div class="image-placeholder">
                [Success Story Visualization and Achievement Summary]
            </div>
            <div class="two-column" style="margin-top: 40px;">
                <div class="feature-box">
                    <h3>Key Achievements</h3>
                    <ul class="success-list">
                        <li>Comprehensive agricultural management ecosystem</li>
                        <li>95.4% accurate AI disease detection system</li>
                        <li>Integrated expert-farmer-marketplace platform</li>
                        <li>Mobile-optimized for Egyptian agricultural context</li>
                        <li>Culturally adapted with Arabic language support</li>
                        <li>Scalable architecture for future expansion</li>
                    </ul>
                </div>
                <div class="feature-box">
                    <h3>Innovation Highlights</h3>
                    <ul class="success-list">
                        <li>First Arabic-supported agricultural AI in Egypt</li>
                        <li>87,000+ image dataset for robust performance</li>
                        <li>Real-world validated with expert panel approval</li>
                        <li>End-to-end solution from diagnosis to treatment</li>
                        <li>Sustainable impact on farmer livelihoods</li>
                        <li>Foundation for digital agriculture transformation</li>
                    </ul>
                </div>
            </div>
            <div class="highlight-banner" style="margin-top: 40px; font-size: 1.4em;">
                "Empowering every Egyptian farmer with AI-driven agricultural intelligence for sustainable food
                security"
            </div>
        </div>

        <!-- Slide 17: Thank You & Contact -->
        <div class="slide title-slide">
            <h1>Thank You for Your Attention</h1>
            <div class="image-placeholder">
                [Team Photo or AgroMind Logo with Contact QR Code]
            </div>
            <div class="feature-box" style="margin: 40px auto; max-width: 700px;">
                <h3>Contact Information & Resources</h3>
                <div style="font-size: 1.4em; line-height: 2.2; text-align: center;">
                    <p><strong>Email:</strong> [Your team email address]</p>
                    <p><strong>GitHub Repository:</strong> [Repository links]</p>
                    <p><strong>Live Demonstration:</strong> [Demo URL if available]</p>
                    <p><strong>Research Paper:</strong> [Publication details if applicable]</p>
                </div>
            </div>
            <h2 style="color: #FFC107; font-size: 2.8em; margin-top: 40px;">Questions & Discussion</h2>
            <p style="font-size: 1.5em; margin-top: 20px;">We welcome your questions, feedback, and suggestions about
                AgroMind</p>
            <div class="highlight-banner" style="margin-top: 30px;">
                Ready to revolutionize Egyptian agriculture together
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← Previous</button>
        <button class="nav-btn" onclick="nextSlide()">Next →</button>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">17</span>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function (e) {
            if (e.key === 'ArrowRight' || e.key === ' ') nextSlide();
            if (e.key === 'ArrowLeft') previousSlide();
        });

        // Animate infographic bars on slide change
        function animateBars() {
            const bars = document.querySelectorAll('.infographic-fill');
            bars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 300);
            });
        }

        // Call animation when slide changes
        const originalShowSlide = showSlide;
        showSlide = function (n) {
            originalShowSlide(n);
            setTimeout(animateBars, 100);
        };
    </script>
</body>

</html>