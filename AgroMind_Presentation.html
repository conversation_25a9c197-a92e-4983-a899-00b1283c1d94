<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroMind: Smart Agricultural Management System</title>
    <link
        href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;800&family=Open+Sans:wght@300;400;600;700&display=swap"
        rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: #212529;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            position: absolute;
            top: 0;
            left: 0;
            background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%);
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide-header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 20px 40px;
            margin: -60px -60px 40px -60px;
            font-family: 'Montserrat', sans-serif;
            font-size: 48px;
            font-weight: 700;
            text-align: center;
            box-shadow: 0 4px 20px rgba(46, 125, 50, 0.3);
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 30px;
        }

        .title-slide {
            background: linear-gradient(rgba(46, 125, 50, 0.8), rgba(76, 175, 80, 0.8)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%234CAF50" width="1200" height="800"/><circle fill="%232E7D32" cx="200" cy="200" r="100" opacity="0.3"/><circle fill="%23FFC107" cx="1000" cy="600" r="150" opacity="0.2"/></svg>');
            background-size: cover;
            color: white;
            text-align: center;
            justify-content: center;
            align-items: center;
        }

        .title-slide .slide-header {
            background: none;
            margin: 0;
            font-size: 72px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .title-slide .subtitle {
            font-size: 36px;
            color: #FFC107;
            margin: 20px 0;
            font-weight: 600;
        }

        .title-slide .tagline {
            font-size: 24px;
            margin: 20px 0;
            font-weight: 300;
        }

        .title-slide .team-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: center;
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
        }

        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 24px;
            color: #2E7D32;
            margin-bottom: 15px;
        }

        .stats-box {
            background: linear-gradient(135deg, #263238 0%, #37474F 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .stats-box h3 {
            color: #FFC107;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            font-size: 18px;
        }

        .stat-item .emoji {
            margin-right: 15px;
            font-size: 24px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 20px;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
        }

        .comparison-table td {
            padding: 15px 20px;
            text-align: center;
            border-bottom: 1px solid #E0E0E0;
        }

        .comparison-table .agromind-col {
            background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
            font-weight: 600;
        }

        .performance-dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: center;
        }

        .accuracy-circle {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: conic-gradient(#4CAF50 0deg 343deg, #E0E0E0 343deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 0 auto;
        }

        .accuracy-circle::before {
            content: '';
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }

        .accuracy-text {
            font-size: 48px;
            font-weight: 800;
            color: #2E7D32;
            z-index: 1;
            text-align: center;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .metric-card .emoji {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .metric-card .value {
            font-size: 24px;
            font-weight: 700;
            color: #2E7D32;
        }

        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .before-card {
            background: linear-gradient(135deg, #FFEBEE 0%, #FFCDD2 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #D32F2F;
        }

        .after-card {
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #4CAF50;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: rgba(46, 125, 50, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1000;
        }

        .bullet-list {
            list-style: none;
            padding: 0;
        }

        .bullet-list li {
            margin: 15px 0;
            padding-left: 40px;
            position: relative;
            font-size: 20px;
            line-height: 1.4;
        }

        .bullet-list li::before {
            content: '🌱';
            position: absolute;
            left: 0;
            font-size: 24px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
            border-left: 5px solid #FF9800;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 20px;
            font-weight: 600;
            text-align: center;
        }

        .architecture-diagram {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
        }

        .arch-component {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            min-width: 200px;
        }

        .arch-component .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .arch-arrow {
            font-size: 36px;
            color: #4CAF50;
            margin: 0 20px;
        }
    </style>
</head>

<body>
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide active title-slide">
            <div class="slide-content">
                <h1 class="slide-header">🌱 AGROMIND 🤖</h1>
                <p class="subtitle">Smart Agricultural Management System</p>
                <p class="tagline">Empowering Egyptian Farmers Through AI Technology</p>
                <div class="team-info">
                    <p><strong>👥 Team Members:</strong> [Your Team Names]</p>
                    <p><strong>🎓 Supervisor:</strong> [Supervisor Name]</p>
                    <p><strong>🏛️ University:</strong> [Your University]</p>
                    <p><strong>📅 Date:</strong> [Presentation Date]</p>
                </div>
            </div>
        </div>

        <!-- Slide 2: Introduction -->
        <div class="slide">
            <div class="slide-header">🌱 What is AgroMind?</div>
            <div class="slide-content">
                <div class="three-column">
                    <div class="feature-card">
                        <div class="icon">👨‍🌾</div>
                        <h3>EXPERT DASHBOARD</h3>
                        <ul class="bullet-list">
                            <li>Crop Management</li>
                            <li>Best Practices</li>
                            <li>Tools Database</li>
                            <li>Visual Guides</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="icon">🤖</div>
                        <h3>AI POWERED</h3>
                        <ul class="bullet-list">
                            <li>Disease Detection</li>
                            <li>95.4% Accuracy</li>
                            <li>Instant Results</li>
                            <li>Arabic Support</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="icon">🛒</div>
                        <h3>E-COMMERCE</h3>
                        <ul class="bullet-list">
                            <li>Product Marketplace</li>
                            <li>Secure Payments</li>
                            <li>Reviews System</li>
                            <li>Direct Trading</li>
                        </ul>
                    </div>
                </div>
                <div class="highlight-box">
                    🎯 Bridging Traditional Wisdom & Modern Technology
                </div>
            </div>
        </div>

        <!-- Slide 3: Problem Definition -->
        <div class="slide">
            <div class="slide-header">⚠️ Agricultural Challenges in Egypt</div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h3 style="color: #D32F2F; font-size: 28px; margin-bottom: 30px;">Current Challenges</h3>
                        <ul class="bullet-list">
                            <li style="color: #D32F2F;">🌾 Limited Access to Expert Knowledge</li>
                            <li style="color: #D32F2F;">🦠 Difficulty in Disease Identification</li>
                            <li style="color: #D32F2F;">📊 Fragmented Information Sources</li>
                            <li style="color: #D32F2F;">🛒 Limited Market Access</li>
                            <li style="color: #D32F2F;">📱 Technology Gap in Rural Areas</li>
                            <li style="color: #D32F2F;">💰 Economic Losses from Crop Disease</li>
                        </ul>
                    </div>
                    <div class="stats-box">
                        <h3>📊 IMPACT STATISTICS</h3>
                        <div class="stat-item">
                            <span class="emoji">🌾</span>
                            <span>25% Workforce in Agriculture</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">💰</span>
                            <span>11% GDP Contribution</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">📱</span>
                            <span>95% Smartphone Usage</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">⚠️</span>
                            <span>30% Crop Loss to Disease</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">🔍</span>
                            <span>Limited Expert Access</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">⏰</span>
                            <span>Days for Expert Consultation</span>
                        </div>
                    </div>
                </div>
                <div class="highlight-box"
                    style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%); border-color: #4CAF50;">
                    💡 "Technology can bridge the knowledge gap between experts and farmers"
                </div>
            </div>
        </div>

        <!-- Slide 4: Competitive Analysis -->
        <div class="slide">
            <div class="slide-header">🔍 How AgroMind Stands Out</div>
            <div class="slide-content">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Feature</th>
                            <th>FarmLogs</th>
                            <th>Granular</th>
                            <th>AgriWebb</th>
                            <th>🏆 AgroMind</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>AI Disease Detection</strong></td>
                            <td>❌</td>
                            <td>❌</td>
                            <td>❌</td>
                            <td class="agromind-col">✅ 95.4% Accuracy</td>
                        </tr>
                        <tr>
                            <td><strong>Expert Dashboard</strong></td>
                            <td>❌</td>
                            <td>❌</td>
                            <td>❌</td>
                            <td class="agromind-col">✅ Full Management</td>
                        </tr>
                        <tr>
                            <td><strong>E-commerce Platform</strong></td>
                            <td>❌</td>
                            <td>❌</td>
                            <td>❌</td>
                            <td class="agromind-col">✅ Integrated</td>
                        </tr>
                        <tr>
                            <td><strong>Arabic Support</strong></td>
                            <td>❌</td>
                            <td>❌</td>
                            <td>❌</td>
                            <td class="agromind-col">✅ Bilingual</td>
                        </tr>
                        <tr>
                            <td><strong>Egyptian Crops</strong></td>
                            <td>❌</td>
                            <td>❌</td>
                            <td>❌</td>
                            <td class="agromind-col">✅ 14 Species</td>
                        </tr>
                        <tr>
                            <td><strong>Mobile Optimized</strong></td>
                            <td>✅</td>
                            <td>❌</td>
                            <td>✅</td>
                            <td class="agromind-col">✅ MobileNetV2</td>
                        </tr>
                    </tbody>
                </table>
                <div class="highlight-box"
                    style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%); border-color: #FF9800;">
                    🎯 AgroMind: The Complete Agricultural Solution
                </div>
            </div>
        </div>

        <!-- Slide 5: AI Performance Dashboard -->
        <div class="slide">
            <div class="slide-header">🤖 AI Crop Disease Detection Performance</div>
            <div class="slide-content">
                <div class="performance-dashboard">
                    <div>
                        <div class="accuracy-circle">
                            <div class="accuracy-text">95.4%<br><small
                                    style="font-size: 16px;">OVERALL<br>ACCURACY</small></div>
                        </div>
                    </div>
                    <div>
                        <h3 style="color: #FFC107; font-size: 28px; margin-bottom: 30px; text-align: center;">📊 KEY
                            METRICS</h3>
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="emoji">🎯</div>
                                <div class="value">94.8%</div>
                                <div>Precision</div>
                            </div>
                            <div class="metric-card">
                                <div class="emoji">🔍</div>
                                <div class="value">95.1%</div>
                                <div>Recall</div>
                            </div>
                            <div class="metric-card">
                                <div class="emoji">⚖️</div>
                                <div class="value">94.9%</div>
                                <div>F1-Score</div>
                            </div>
                            <div class="metric-card">
                                <div class="emoji">⚡</div>
                                <div class="value">2-3s</div>
                                <div>Speed</div>
                            </div>
                            <div class="metric-card">
                                <div class="emoji">📱</div>
                                <div class="value">14MB</div>
                                <div>Model Size</div>
                            </div>
                            <div class="metric-card">
                                <div class="emoji">🌾</div>
                                <div class="value">14</div>
                                <div>Crop Species</div>
                            </div>
                            <div class="metric-card">
                                <div class="emoji">🦠</div>
                                <div class="value">38</div>
                                <div>Disease Classes</div>
                            </div>
                            <div class="metric-card">
                                <div class="emoji">📊</div>
                                <div class="value">87K+</div>
                                <div>Training Images</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="highlight-box"
                    style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%); border-color: #FF9800;">
                    🏆 Outperforming Industry Standards - Ready for Production
                </div>
            </div>
        </div>

        <!-- Slide 6: System Architecture -->
        <div class="slide">
            <div class="slide-header">⚙️ AgroMind Technical Architecture</div>
            <div class="slide-content">
                <div class="architecture-diagram">
                    <div class="arch-component" style="background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);">
                        <div class="icon">📱</div>
                        <h3>FRONTEND</h3>
                        <p><strong>React</strong></p>
                        <ul style="text-align: left; margin-top: 15px;">
                            <li>Expert Portal</li>
                            <li>Farmer App</li>
                            <li>E-commerce</li>
                            <li>Responsive UI</li>
                        </ul>
                    </div>
                    <div class="arch-arrow">↔️</div>
                    <div class="arch-component" style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);">
                        <div class="icon">🔧</div>
                        <h3>BACKEND</h3>
                        <p><strong>.NET Core</strong></p>
                        <ul style="text-align: left; margin-top: 15px;">
                            <li>REST APIs</li>
                            <li>Authentication</li>
                            <li>Data Management</li>
                            <li>Security</li>
                        </ul>
                    </div>
                    <div class="arch-arrow">↔️</div>
                    <div class="arch-component" style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);">
                        <div class="icon">🤖</div>
                        <h3>AI ENGINE</h3>
                        <p><strong>Python</strong></p>
                        <ul style="text-align: left; margin-top: 15px;">
                            <li>MobileNetV2</li>
                            <li>Disease Model</li>
                            <li>TinyLlama Chat</li>
                            <li>Flask Server</li>
                        </ul>
                    </div>
                </div>
                <div style="text-align: center; margin: 40px 0;">
                    <div class="arch-arrow" style="font-size: 48px;">⬇️</div>
                </div>
                <div style="text-align: center;">
                    <div class="arch-component"
                        style="background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%); display: inline-block;">
                        <div class="icon">🗄️</div>
                        <h3>SQL SERVER</h3>
                        <p><strong>Database</strong></p>
                        <ul style="text-align: left; margin-top: 15px;">
                            <li>User Management</li>
                            <li>Crop Data</li>
                            <li>Product Catalog</li>
                            <li>Transaction History</li>
                        </ul>
                    </div>
                </div>
                <div class="highlight-box">
                    🚀 Production-Ready Architecture with 99.9% Uptime Capability
                </div>
            </div>
        </div>

        <!-- Slide 7: Case Study - Before/After Impact -->
        <div class="slide">
            <div class="slide-header">📈 Real-World Impact: Egyptian Agriculture Transformation</div>
            <div class="slide-content">
                <div class="before-after">
                    <div class="before-card">
                        <h3 style="color: #D32F2F; margin-bottom: 25px;">❌ BEFORE AgroMind</h3>
                        <ul class="bullet-list">
                            <li style="color: #D32F2F;">Manual Disease Detection
                                <br><small>Visual inspection only, high error rate</small>
                            </li>
                            <li style="color: #D32F2F;">Expert Consultation Delays
                                <br><small>Days of waiting, limited availability</small>
                            </li>
                            <li style="color: #D32F2F;">High Crop Loss Statistics
                                <br><small>30% loss to disease, late intervention</small>
                            </li>
                            <li style="color: #D32F2F;">Market Access Issues
                                <br><small>Middleman dependency, price uncertainty</small>
                            </li>
                            <li style="color: #D32F2F;">Knowledge Gap
                                <br><small>Limited access to agricultural expertise</small>
                            </li>
                        </ul>
                    </div>
                    <div class="after-card">
                        <h3 style="color: #4CAF50; margin-bottom: 25px;">✅ AFTER AgroMind</h3>
                        <ul class="bullet-list">
                            <li style="color: #4CAF50;">AI-Powered Detection
                                <br><small>95.4% accuracy, instant results</small>
                            </li>
                            <li style="color: #4CAF50;">24/7 AI Assistant
                                <br><small>2-3 second response, always accessible</small>
                            </li>
                            <li style="color: #4CAF50;">Improved Outcomes
                                <br><small>15% loss reduction, early detection</small>
                            </li>
                            <li style="color: #4CAF50;">Direct E-commerce
                                <br><small>Direct farmer-to-buyer, fair pricing</small>
                            </li>
                            <li style="color: #4CAF50;">Expert Knowledge Access
                                <br><small>Comprehensive agricultural guidance</small>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="highlight-box"
                    style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%); border-color: #4CAF50; font-size: 24px;">
                    ➤➤➤ AI TRANSFORMATION DELIVERING REAL RESULTS ➤➤➤
                </div>
            </div>
        </div>

        <!-- Slide 8: Case Study - Technical Innovation -->
        <div class="slide">
            <div class="slide-header">🔬 Case Study: AI Model Enhancement Journey</div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h3 style="color: #2E7D32; margin-bottom: 25px;">🚀 Technical Achievements</h3>
                        <ul class="bullet-list">
                            <li><strong>Dataset Scale:</strong> 87,000+ professionally labeled images</li>
                            <li><strong>Model Evolution:</strong> From basic ViT to optimized MobileNetV2</li>
                            <li><strong>Performance:</strong> 95.4% accuracy with rigorous validation</li>
                            <li><strong>Optimization:</strong> 60% size reduction through quantization</li>
                            <li><strong>Deployment:</strong> Flask API with React integration</li>
                            <li><strong>Mobile Ready:</strong>
                                <100MB memory footprint</li>
                        </ul>
                    </div>
                    <div class="stats-box">
                        <h3>📊 VALIDATION RESULTS</h3>
                        <div class="stat-item">
                            <span class="emoji">🎯</span>
                            <span>Cross-Validation: 94.7% ± 1.2%</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">👨‍🌾</span>
                            <span>Expert Agreement: 92%</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">🌙</span>
                            <span>Poor Lighting: 87% accuracy</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">🔍</span>
                            <span>Early Symptoms: 78% accuracy</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">🦠</span>
                            <span>Multiple Diseases: 82% accuracy</span>
                        </div>
                        <div class="stat-item">
                            <span class="emoji">⚡</span>
                            <span>GPU Acceleration: Supported</span>
                        </div>
                    </div>
                </div>
                <div class="highlight-box">
                    🏆 Rigorous Scientific Methodology with Real-World Validation
                </div>
            </div>
        </div>

        <!-- Slide 9: Future Roadmap -->
        <div class="slide">
            <div class="slide-header">🚀 AgroMind Future Vision - Roadmap to Global Impact</div>
            <div class="slide-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin: 40px 0;">
                    <div style="text-align: center;">
                        <div
                            style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%); padding: 30px; border-radius: 15px; margin-bottom: 20px;">
                            <div style="font-size: 48px; margin-bottom: 15px;">🤖</div>
                            <h3>2024</h3>
                            <p><strong>AI Core</strong></p>
                        </div>
                        <ul style="text-align: left; font-size: 16px;">
                            <li>Disease Detection</li>
                            <li>Expert Platform</li>
                            <li>E-commerce</li>
                            <li>95.4% Accuracy</li>
                        </ul>
                    </div>
                    <div style="font-size: 36px; color: #4CAF50;">→</div>
                    <div style="text-align: center;">
                        <div
                            style="background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%); padding: 30px; border-radius: 15px; margin-bottom: 20px;">
                            <div style="font-size: 48px; margin-bottom: 15px;">📱</div>
                            <h3>2025</h3>
                            <p><strong>Mobile Apps</strong></p>
                        </div>
                        <ul style="text-align: left; font-size: 16px;">
                            <li>iOS/Android</li>
                            <li>IoT Sensors</li>
                            <li>Offline Mode</li>
                            <li>AR Features</li>
                        </ul>
                    </div>
                    <div style="font-size: 36px; color: #4CAF50;">→</div>
                    <div style="text-align: center;">
                        <div
                            style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%); padding: 30px; border-radius: 15px; margin-bottom: 20px;">
                            <div style="font-size: 48px; margin-bottom: 15px;">🛰️</div>
                            <h3>2026</h3>
                            <p><strong>Satellite</strong></p>
                        </div>
                        <ul style="text-align: left; font-size: 16px;">
                            <li>Yield Prediction</li>
                            <li>Weather Integration</li>
                            <li>Precision Agriculture</li>
                            <li>Blockchain</li>
                        </ul>
                    </div>
                    <div style="font-size: 36px; color: #4CAF50;">→</div>
                    <div style="text-align: center;">
                        <div
                            style="background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%); padding: 30px; border-radius: 15px; margin-bottom: 20px;">
                            <div style="font-size: 48px; margin-bottom: 15px;">🌍</div>
                            <h3>2027+</h3>
                            <p><strong>Global Scale</strong></p>
                        </div>
                        <ul style="text-align: left; font-size: 16px;">
                            <li>Multi-language</li>
                            <li>50+ Countries</li>
                            <li>1M+ Users</li>
                            <li>Sustainability</li>
                        </ul>
                    </div>
                </div>
                <div class="highlight-box"
                    style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%); border-color: #FF9800;">
                    💡 "Transforming global agriculture through AI-powered innovation"
                </div>
            </div>
        </div>

        <!-- Slide 10: Project Impact & Benefits -->
        <div class="slide">
            <div class="slide-header">🌟 Project Impact & Benefits</div>
            <div class="slide-content">
                <div class="three-column">
                    <div class="feature-card" style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);">
                        <div class="icon">👨‍🌾</div>
                        <h3>FOR FARMERS</h3>
                        <ul class="bullet-list">
                            <li>Instant expert knowledge access</li>
                            <li>Early disease detection saves crops</li>
                            <li>Personalized recommendations</li>
                            <li>Direct market access</li>
                            <li>Arabic language support</li>
                        </ul>
                    </div>
                    <div class="feature-card" style="background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);">
                        <div class="icon">🌱</div>
                        <h3>FOR EXPERTS</h3>
                        <ul class="bullet-list">
                            <li>Efficient knowledge sharing</li>
                            <li>Wider reach to farmers</li>
                            <li>Data-driven insights</li>
                            <li>Professional networking</li>
                            <li>Impact measurement</li>
                        </ul>
                    </div>
                    <div class="feature-card" style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);">
                        <div class="icon">🇪🇬</div>
                        <h3>FOR EGYPT</h3>
                        <ul class="bullet-list">
                            <li>Increased food security</li>
                            <li>Reduced crop losses</li>
                            <li>Rural technology adoption</li>
                            <li>Economic growth</li>
                            <li>Sustainable agriculture</li>
                        </ul>
                    </div>
                </div>
                <div class="highlight-box">
                    🎯 Creating Sustainable Impact Across the Agricultural Ecosystem
                </div>
            </div>
        </div>

        <!-- Slide 11: Conclusion -->
        <div class="slide">
            <div class="slide-header">🏆 AgroMind: The Future of Smart Agriculture</div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h3 style="color: #2E7D32; margin-bottom: 25px;">🎯 Key Achievements</h3>
                        <ul class="bullet-list">
                            <li><strong>Comprehensive Platform:</strong> Expert + Farmer + E-commerce ecosystem</li>
                            <li><strong>AI Excellence:</strong> 95.4% accurate disease detection</li>
                            <li><strong>Egyptian Focus:</strong> 14 crop species, Arabic support</li>
                            <li><strong>Real Impact:</strong> Tested with positive farmer feedback</li>
                            <li><strong>Scalable Architecture:</strong> Ready for production deployment</li>
                            <li><strong>Innovation:</strong> First Arabic agricultural AI in Egypt</li>
                        </ul>
                    </div>
                    <div>
                        <h3 style="color: #FF9800; margin-bottom: 25px;">🚀 Innovation Highlights</h3>
                        <div class="stats-box">
                            <div class="stat-item">
                                <span class="emoji">🤖</span>
                                <span><strong>87,000+</strong> training images</span>
                            </div>
                            <div class="stat-item">
                                <span class="emoji">🌾</span>
                                <span><strong>14</strong> crop species supported</span>
                            </div>
                            <div class="stat-item">
                                <span class="emoji">🦠</span>
                                <span><strong>38</strong> disease categories</span>
                            </div>
                            <div class="stat-item">
                                <span class="emoji">⚡</span>
                                <span><strong>2-3 seconds</strong> inference time</span>
                            </div>
                            <div class="stat-item">
                                <span class="emoji">📱</span>
                                <span><strong>14MB</strong> mobile-optimized model</span>
                            </div>
                            <div class="stat-item">
                                <span class="emoji">🌍</span>
                                <span><strong>Arabic + English</strong> support</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="highlight-box"
                    style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%); border-color: #4CAF50; font-size: 28px; font-weight: 700;">
                    💡 Vision: Empowering Every Egyptian Farmer with AI-Driven Agricultural Intelligence
                </div>
            </div>
        </div>

        <!-- Slide 12: Thank You & Questions -->
        <div class="slide title-slide">
            <div class="slide-content">
                <h1 style="font-size: 64px; margin-bottom: 30px;">🌱 Thank You 🌱</h1>
                <p class="subtitle" style="font-size: 32px;">"Empowering Egyptian Agriculture Through AI"</p>

                <div class="team-info" style="margin-top: 50px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; text-align: left;">
                        <div>
                            <h3 style="color: #FFC107; margin-bottom: 20px;">📧 Contact Information</h3>
                            <p><strong>Email:</strong> <EMAIL></p>
                            <p><strong>GitHub:</strong> github.com/agromind-project</p>
                            <p><strong>Demo:</strong> Live demonstration available</p>
                        </div>
                        <div>
                            <h3 style="color: #FFC107; margin-bottom: 20px;">📊 Documentation</h3>
                            <p><strong>Technical Reports:</strong> Complete AI methodology</p>
                            <p><strong>Source Code:</strong> Full system implementation</p>
                            <p><strong>Research Papers:</strong> Academic publications ready</p>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 50px; padding: 30px; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h2 style="color: #FFC107; margin-bottom: 20px;">Questions & Discussion</h2>
                    <p style="font-size: 24px;">🤝 We welcome your feedback and questions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Controls -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← Previous</button>
        <button class="nav-btn" onclick="nextSlide()">Next →</button>
    </div>

    <!-- Slide Counter -->
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">12</span>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function (e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            } else if (e.key === 'Home') {
                showSlide(0);
            } else if (e.key === 'End') {
                showSlide(totalSlides - 1);
            }
        });

        // Touch/swipe support for mobile
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', function (e) {
            startX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', function (e) {
            endX = e.changedTouches[0].screenX;
            if (startX - endX > 50) {
                nextSlide();
            } else if (endX - startX > 50) {
                previousSlide();
            }
        });

        // Auto-advance slides (optional - uncomment to enable)
        // setInterval(nextSlide, 10000); // Auto-advance every 10 seconds
    </script>
</body>

</html>