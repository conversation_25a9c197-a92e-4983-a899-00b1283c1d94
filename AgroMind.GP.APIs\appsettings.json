{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "AgroMind.GP.Service.Services": "Information", // To see logs from TokenService
      "AgroMind.GP.APIs": "Information" // To see logs from IdentityServiceExtension
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:agromind-sql-server.database.windows.net,1433;Initial Catalog=agromind-db;Persist Security Info=False;User ID=agromind-admin;Password={your_password};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "RedisConnection": ""
  },
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "Port": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "ciduhpxgftiqfedf"
  },
  "JWT": {
    "key": "SuperSecureAuthenticationKey123456789!",
    "ValidIssuer": "https://agromind-backend-g6g9beexdpg8heeg.uaenorth-01.azurewebsites.net",
    //"ValidAudience": "MySecureKey",
    "ValidAudience": "MySecureClientApp", // Make this more specific, the URL of  frontend
    "DurationInDays": "4"
  }
}