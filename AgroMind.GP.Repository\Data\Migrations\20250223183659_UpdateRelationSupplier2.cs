﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AgroMind.GP.Repository.Data.Migrations
{
	/// <inheritdoc />
	public partial class UpdateRelationSupplier2 : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				name: "FK_Products_Categories_CategoryId",
				table: "Products");

			migrationBuilder.DropPrimaryKey(
				name: "PK_Categories",
				table: "Categories");

			migrationBuilder.RenameTable(
				name: "Categories",
				newName: "Category");

			migrationBuilder.AddPrimaryKey(
				name: "PK_Category",
				table: "Category",
				column: "Id");

			migrationBuilder.AddForeignKey(
				name: "FK_Products_Category_CategoryId",
				table: "Products",
				column: "CategoryId",
				principalTable: "Category",
				principalColumn: "Id",
				onDelete: ReferentialAction.SetNull);
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				name: "FK_Products_Category_CategoryId",
				table: "Products");

			migrationBuilder.DropPrimaryKey(
				name: "PK_Category",
				table: "Category");

			migrationBuilder.RenameTable(
				name: "Category",
				newName: "Categories");

			migrationBuilder.AddPrimaryKey(
				name: "PK_Categories",
				table: "Categories",
				column: "Id");

			migrationBuilder.AddForeignKey(
				name: "FK_Products_Categories_CategoryId",
				table: "Products",
				column: "CategoryId",
				principalTable: "Categories",
				principalColumn: "Id",
				onDelete: ReferentialAction.SetNull);
		}
	}
}
