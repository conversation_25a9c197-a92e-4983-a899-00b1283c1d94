# 📚 References and Bibliography - AgroMind Project

## **Academic and Research References**

### **Agricultural Technology and AI Applications**

1. **<PERSON>, <PERSON>, & <PERSON>, M.** (2015). An open access repository of images on plant health to enable the development of mobile disease diagnostics. *arXiv preprint arXiv:1511.08060*.

2. **<PERSON>, S<PERSON>, <PERSON>, <PERSON>, & <PERSON>, M.** (2016). Using deep learning for image-based plant disease detection. *Frontiers in plant science*, 7, 1419.

3. **<PERSON>, J. G. A.** (2018). Factors influencing the use of deep learning for plant disease recognition. *Biosystems Engineering*, 172, 84-91.

4. **<PERSON><PERSON>, A., & <PERSON>-<PERSON>ú, F. X.** (2018). Deep learning in agriculture: A survey. *Computers and electronics in agriculture*, 147, 70-90.

5. **<PERSON>, M. <PERSON>, <PERSON>t<PERSON>, J<PERSON>, & <PERSON>, K. M.** (2019). Plant disease detection and classification by deep learning. *Plants*, 8(11), 468.

### **Egyptian Agriculture and Regional Studies**

6. **FAO** (2020). *The State of Food Security and Nutrition in the World 2020*. Food and Agriculture Organization of the United Nations, Rome.

7. **CAPMAS** (2021). *Statistical Yearbook - Agriculture Sector*. Central Agency for Public Mobilization and Statistics, Egypt.

8. **El-Ramady, H., Alshaal, T., Bakr, N., Elbana, T., Mohamed, E., & Belal, A. A.** (2019). Agriculture and food security in Egypt. *Springer*.

9. **Abdelaal, K., & Hafez, Y.** (2018). The role of plant growth promoting rhizobacteria in promoting growth and alleviating salt stress in cucumber plants. *Egyptian Journal of Botany*, 58(2), 141-152.

### **Computer Vision and Deep Learning**

10. **Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., ... & Houlsby, N.** (2020). An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*.

11. **Sandler, M., Howard, A., Zhu, M., Zhmoginov, A., & Chen, L. C.** (2018). MobileNets: Efficient convolutional neural networks for mobile vision applications. *Proceedings of the IEEE conference on computer vision and pattern recognition*, 4510-4520.

12. **Howard, A. G., Zhu, M., Chen, B., Kalenichenko, D., Wang, W., Weyand, T., ... & Adam, H.** (2017). MobileNets: Efficient convolutional neural networks for mobile vision applications. *arXiv preprint arXiv:1704.04861*.

13. **Tan, M., & Le, Q.** (2019). EfficientNet: Rethinking model scaling for convolutional neural networks. *International conference on machine learning*, 6105-6114.

### **Natural Language Processing and Conversational AI**

14. **Anil, R., Dai, A. M., Firat, O., Johnson, M., Lepikhin, D., Passos, A., ... & Wu, Y.** (2023). PaLM 2 Technical Report. *arXiv preprint arXiv:2305.10403*.

15. **Team, G., Anil, R., Borgeaud, S., Wu, Y., Alayrac, J. B., Yu, J., ... & Silver, D.** (2023). Gemini: a family of highly capable multimodal models. *arXiv preprint arXiv:2312.11805*.

16. **Brown, T., Mann, B., Ryder, N., Subbiah, M., Kaplan, J. D., Dhariwal, P., ... & Amodei, D.** (2020). Language models are few-shot learners. *Advances in neural information processing systems*, 33, 1877-1901.

## **Hugging Face Models and Transformers**

### **Pre-trained Models Used**

17. **linkanjarad** (2023). *mobilenet_v2_1.0_224-plant-disease-identification*. Hugging Face Model Hub. Retrieved from https://huggingface.co/linkanjarad/mobilenet_v2_1.0_224-plant-disease-identification

18. **Google** (2021). *vit-base-patch16-224*. Hugging Face Model Hub. Retrieved from https://huggingface.co/google/vit-base-patch16-224

19. **kerem** (2022). *plant-village dataset*. Hugging Face Datasets. Retrieved from https://huggingface.co/datasets/kerem/plant-village

### **Alternative Agricultural Models Evaluated**

20. **mrSoul7766** (2023). *AgriQBot*. Hugging Face Model Hub. Retrieved from https://huggingface.co/mrSoul7766/AgriQBot

21. **Sicnarf01** (2023). *agrigpt_expert*. Hugging Face Model Hub. Retrieved from https://huggingface.co/Sicnarf01/agrigpt_expert

22. **soma77** (2023). *agriculture_advisor*. Hugging Face Model Hub. Retrieved from https://huggingface.co/soma77/agriculture_advisor

### **Transformers Library**

23. **Wolf, T., Debut, L., Sanh, V., Chaumond, J., Delangue, C., Moi, A., ... & Rush, A. M.** (2020). Transformers: State-of-the-art natural language processing. *Proceedings of the 2020 conference on empirical methods in natural language processing: system demonstrations*, 38-45.

## **Technical Documentation and Standards**

### **Web Development Frameworks**

24. **Facebook Inc.** (2023). *React - A JavaScript library for building user interfaces*. Retrieved from https://reactjs.org/

25. **Microsoft Corporation** (2023). *.NET Core Documentation*. Retrieved from https://docs.microsoft.com/en-us/dotnet/core/

26. **Microsoft Corporation** (2023). *Entity Framework Core Documentation*. Retrieved from https://docs.microsoft.com/en-us/ef/core/

### **Machine Learning Frameworks**

27. **Abadi, M., Agarwal, A., Barham, P., Brevdo, E., Chen, Z., Citro, C., ... & Zheng, X.** (2016). TensorFlow: Large-scale machine learning on heterogeneous systems. *arXiv preprint arXiv:1603.04467*.

28. **Paszke, A., Gross, S., Massa, F., Lerer, A., Bradbury, J., Chanan, G., ... & Chintala, S.** (2019). PyTorch: An imperative style, high-performance deep learning library. *Advances in neural information processing systems*, 32.

29. **Chollet, F.** (2015). Keras. Retrieved from https://keras.io

## **Datasets and Data Sources**

### **Primary Datasets**

30. **Hughes, D. P., & Salathé, M.** (2015). *PlantVillage Dataset*. Retrieved from https://github.com/spMohanty/PlantVillage-Dataset

31. **Kaggle** (2020). *Plant Disease Recognition Dataset*. Retrieved from https://www.kaggle.com/datasets/vipoooool/new-plant-diseases-dataset

32. **UC Irvine Machine Learning Repository** (2019). *Crop Recommendation Dataset*. Retrieved from https://archive.ics.uci.edu/ml/datasets/

### **Agricultural Data Sources**

33. **Ministry of Agriculture and Land Reclamation, Egypt** (2021). *Agricultural Statistics Bulletin*. Cairo, Egypt.

34. **FAOSTAT** (2023). *Food and Agriculture Data*. Food and Agriculture Organization of the United Nations. Retrieved from http://www.fao.org/faostat/

## **Software Tools and Libraries**

### **Development Tools**

35. **Node.js Foundation** (2023). *Node.js Runtime Environment*. Retrieved from https://nodejs.org/

36. **Python Software Foundation** (2023). *Python Programming Language*. Retrieved from https://www.python.org/

37. **Pallets Projects** (2023). *Flask Web Framework*. Retrieved from https://flask.palletsprojects.com/

### **Data Processing Libraries**

38. **McKinney, W.** (2010). Data structures for statistical computing in python. *Proceedings of the 9th Python in Science Conference*, 445, 51-56.

39. **Harris, C. R., Millman, K. J., Van Der Walt, S. J., Gommers, R., Virtanen, P., Cournapeau, D., ... & Oliphant, T. E.** (2020). Array programming with NumPy. *Nature*, 585(7825), 357-362.

40. **Pedregosa, F., Varoquaux, G., Gramfort, A., Michel, V., Thirion, B., Grisel, O., ... & Duchesnay, E.** (2011). Scikit-learn: Machine learning in Python. *Journal of machine learning research*, 12, 2825-2830.

## **Standards and Protocols**

### **Web Standards**

41. **W3C** (2021). *HTML5 Specification*. World Wide Web Consortium. Retrieved from https://www.w3.org/TR/html52/

42. **W3C** (2021). *CSS3 Specification*. World Wide Web Consortium. Retrieved from https://www.w3.org/Style/CSS/

43. **ECMA International** (2021). *ECMAScript 2021 Language Specification*. Retrieved from https://www.ecma-international.org/

### **API Standards**

44. **Fielding, R. T.** (2000). *Architectural styles and the design of network-based software architectures* (Doctoral dissertation, University of California, Irvine).

45. **OpenAPI Initiative** (2023). *OpenAPI Specification*. Retrieved from https://swagger.io/specification/

## **Research Methodology References**

### **Software Engineering Practices**

46. **Beck, K., Beedle, M., Van Bennekum, A., Cockburn, A., Cunningham, W., Fowler, M., ... & Thomas, D.** (2001). *Manifesto for agile software development*. Retrieved from http://agilemanifesto.org/

47. **Fowler, M.** (2018). *Refactoring: improving the design of existing code*. Addison-Wesley Professional.

### **User Experience Design**

48. **Norman, D.** (2013). *The design of everyday things: Revised and expanded edition*. Basic books.

49. **Nielsen, J.** (1994). *Usability engineering*. Morgan Kaufmann.

## **Future Work and Planned Publications**

### **Submitted/In Preparation**

42. **[Author Name]** (2025). AgroMind: A Comprehensive AI-Powered Agricultural Management System for Egyptian Farmers. *Journal of Agricultural Informatics* (In Preparation).

43. **[Author Name]** (2025). Cultural Adaptation of AI Systems for Agricultural Applications in Developing Countries. *Computers and Electronics in Agriculture* (In Preparation).

44. **[Author Name]** (2025). Mobile-Optimized Deep Learning for Plant Disease Detection in Resource-Constrained Environments. *IEEE Transactions on Agricultural Engineering* (In Preparation).

---

## **Conference Presentations (Planned)**

- International Conference on Agricultural Engineering (ICAE 2025)
- IEEE International Conference on Computer Vision Applications (ICCVA 2025)
- ACM Conference on Computing for Sustainability (CompSust 2025)

---

*Last Updated: June 2025*
*Total References: 44*

**Note:** This bibliography follows academic citation standards and includes all major technical resources, datasets, frameworks, and research papers that contributed to the development of the AgroMind agricultural management system.
