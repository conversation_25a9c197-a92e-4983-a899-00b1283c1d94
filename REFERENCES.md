# 📚 References and Bibliography - AgroMind Project

## **Academic and Research References**

### **Agricultural Technology and AI Applications**

1. **<PERSON>, <PERSON>, & <PERSON>, M.** (2015). An open access repository of images on plant health to enable the development of mobile disease diagnostics. *arXiv preprint arXiv:1511.08060*.

2. **<PERSON>, S<PERSON>, <PERSON>, <PERSON>, & <PERSON>, M.** (2016). Using deep learning for image-based plant disease detection. *Frontiers in plant science*, 7, 1419.

3. **<PERSON>, J. G. A.** (2018). Factors influencing the use of deep learning for plant disease recognition. *Biosystems Engineering*, 172, 84-91.

4. **<PERSON><PERSON>, A., & <PERSON>-<PERSON>ú, F. X.** (2018). Deep learning in agriculture: A survey. *Computers and electronics in agriculture*, 147, 70-90.

5. **<PERSON>, M. <PERSON>, <PERSON>t<PERSON>, J<PERSON>, & <PERSON>, K. M.** (2019). Plant disease detection and classification by deep learning. *Plants*, 8(11), 468.

### **Egyptian Agriculture and Regional Studies**

6. **FAO** (2020). *The State of Food Security and Nutrition in the World 2020*. Food and Agriculture Organization of the United Nations, Rome.

7. **CAPMAS** (2021). *Statistical Yearbook - Agriculture Sector*. Central Agency for Public Mobilization and Statistics, Egypt.

8. **El-Ramady, H., Alshaal, T., Bakr, N., Elbana, T., Mohamed, E., & Belal, A. A.** (2019). Agriculture and food security in Egypt. *Springer*.

9. **Abdelaal, K., & Hafez, Y.** (2018). The role of plant growth promoting rhizobacteria in promoting growth and alleviating salt stress in cucumber plants. *Egyptian Journal of Botany*, 58(2), 141-152.

### **Computer Vision and Deep Learning**

10. **Sandler, M., Howard, A., Zhu, M., Zhmoginov, A., & Chen, L. C.** (2018). MobileNets: Efficient convolutional neural networks for mobile vision applications. *Proceedings of the IEEE conference on computer vision and pattern recognition*, 4510-4520.

11. **Howard, A. G., Zhu, M., Chen, B., Kalenichenko, D., Wang, W., Weyand, T., ... & Adam, H.** (2017). MobileNets: Efficient convolutional neural networks for mobile vision applications. *arXiv preprint arXiv:1704.04861*.

12. **Tan, M., & Le, Q.** (2019). EfficientNet: Rethinking model scaling for convolutional neural networks. *International conference on machine learning*, 6105-6114.

### **Natural Language Processing and Conversational AI**

13. **Anil, R., Dai, A. M., Firat, O., Johnson, M., Lepikhin, D., Passos, A., ... & Wu, Y.** (2023). PaLM 2 Technical Report. *arXiv preprint arXiv:2305.10403*.

14. **Team, G., Anil, R., Borgeaud, S., Wu, Y., Alayrac, J. B., Yu, J., ... & Silver, D.** (2023). Gemini: a family of highly capable multimodal models. *arXiv preprint arXiv:2312.11805*.

15. **Brown, T., Mann, B., Ryder, N., Subbiah, M., Kaplan, J. D., Dhariwal, P., ... & Amodei, D.** (2020). Language models are few-shot learners. *Advances in neural information processing systems*, 33, 1877-1901.

## **Technical Documentation and Standards**

### **Web Development Frameworks**

16. **Facebook Inc.** (2023). *React - A JavaScript library for building user interfaces*. Retrieved from https://reactjs.org/

17. **Microsoft Corporation** (2023). *.NET Core Documentation*. Retrieved from https://docs.microsoft.com/en-us/dotnet/core/

18. **Microsoft Corporation** (2023). *Entity Framework Core Documentation*. Retrieved from https://docs.microsoft.com/en-us/ef/core/

### **Machine Learning Frameworks**

19. **Abadi, M., Agarwal, A., Barham, P., Brevdo, E., Chen, Z., Citro, C., ... & Zheng, X.** (2016). TensorFlow: Large-scale machine learning on heterogeneous systems. *arXiv preprint arXiv:1603.04467*.

20. **Paszke, A., Gross, S., Massa, F., Lerer, A., Bradbury, J., Chanan, G., ... & Chintala, S.** (2019). PyTorch: An imperative style, high-performance deep learning library. *Advances in neural information processing systems*, 32.

21. **Chollet, F.** (2015). Keras. Retrieved from https://keras.io

## **Datasets and Data Sources**

### **Primary Datasets**

22. **Hughes, D. P., & Salathé, M.** (2015). *PlantVillage Dataset*. Retrieved from https://github.com/spMohanty/PlantVillage-Dataset

23. **Kaggle** (2020). *Plant Disease Recognition Dataset*. Retrieved from https://www.kaggle.com/datasets/vipoooool/new-plant-diseases-dataset

24. **UC Irvine Machine Learning Repository** (2019). *Crop Recommendation Dataset*. Retrieved from https://archive.ics.uci.edu/ml/datasets/

### **Agricultural Data Sources**

25. **Ministry of Agriculture and Land Reclamation, Egypt** (2021). *Agricultural Statistics Bulletin*. Cairo, Egypt.

26. **FAOSTAT** (2023). *Food and Agriculture Data*. Food and Agriculture Organization of the United Nations. Retrieved from http://www.fao.org/faostat/

## **Software Tools and Libraries**

### **Development Tools**

27. **Node.js Foundation** (2023). *Node.js Runtime Environment*. Retrieved from https://nodejs.org/

28. **Python Software Foundation** (2023). *Python Programming Language*. Retrieved from https://www.python.org/

29. **Pallets Projects** (2023). *Flask Web Framework*. Retrieved from https://flask.palletsprojects.com/

### **Data Processing Libraries**

30. **McKinney, W.** (2010). Data structures for statistical computing in python. *Proceedings of the 9th Python in Science Conference*, 445, 51-56.

31. **Harris, C. R., Millman, K. J., Van Der Walt, S. J., Gommers, R., Virtanen, P., Cournapeau, D., ... & Oliphant, T. E.** (2020). Array programming with NumPy. *Nature*, 585(7825), 357-362.

32. **Pedregosa, F., Varoquaux, G., Gramfort, A., Michel, V., Thirion, B., Grisel, O., ... & Duchesnay, E.** (2011). Scikit-learn: Machine learning in Python. *Journal of machine learning research*, 12, 2825-2830.

## **Standards and Protocols**

### **Web Standards**

33. **W3C** (2021). *HTML5 Specification*. World Wide Web Consortium. Retrieved from https://www.w3.org/TR/html52/

34. **W3C** (2021). *CSS3 Specification*. World Wide Web Consortium. Retrieved from https://www.w3.org/Style/CSS/

35. **ECMA International** (2021). *ECMAScript 2021 Language Specification*. Retrieved from https://www.ecma-international.org/

### **API Standards**

36. **Fielding, R. T.** (2000). *Architectural styles and the design of network-based software architectures* (Doctoral dissertation, University of California, Irvine).

37. **OpenAPI Initiative** (2023). *OpenAPI Specification*. Retrieved from https://swagger.io/specification/

## **Research Methodology References**

### **Software Engineering Practices**

38. **Beck, K., Beedle, M., Van Bennekum, A., Cockburn, A., Cunningham, W., Fowler, M., ... & Thomas, D.** (2001). *Manifesto for agile software development*. Retrieved from http://agilemanifesto.org/

39. **Fowler, M.** (2018). *Refactoring: improving the design of existing code*. Addison-Wesley Professional.

### **User Experience Design**

40. **Norman, D.** (2013). *The design of everyday things: Revised and expanded edition*. Basic books.

41. **Nielsen, J.** (1994). *Usability engineering*. Morgan Kaufmann.

## **Future Work and Planned Publications**

### **Submitted/In Preparation**

42. **[Author Name]** (2025). AgroMind: A Comprehensive AI-Powered Agricultural Management System for Egyptian Farmers. *Journal of Agricultural Informatics* (In Preparation).

43. **[Author Name]** (2025). Cultural Adaptation of AI Systems for Agricultural Applications in Developing Countries. *Computers and Electronics in Agriculture* (In Preparation).

44. **[Author Name]** (2025). Mobile-Optimized Deep Learning for Plant Disease Detection in Resource-Constrained Environments. *IEEE Transactions on Agricultural Engineering* (In Preparation).

---

## **Conference Presentations (Planned)**

- International Conference on Agricultural Engineering (ICAE 2025)
- IEEE International Conference on Computer Vision Applications (ICCVA 2025)
- ACM Conference on Computing for Sustainability (CompSust 2025)

---

*Last Updated: June 2025*
*Total References: 44*

**Note:** This bibliography follows academic citation standards and includes all major technical resources, datasets, frameworks, and research papers that contributed to the development of the AgroMind agricultural management system.
