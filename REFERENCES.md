# References - AgroMind Project

## **ACADEMIC BIBLIOGRAPHY**

**[1]** <PERSON>, M<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C., <PERSON><PERSON><PERSON> & <PERSON>, X. (2016). TensorFlow: Large-scale machine learning on heterogeneous systems. *arXiv preprint arXiv:1603.04467*.

**[2]** <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, D., Passos, A., ... & Wu, Y. (2023). PaLM 2 Technical Report. *arXiv preprint arXiv:2305.10403*.

**[3]** <PERSON>bedo, J. G. A. (2018). Factors influencing the use of deep learning for plant disease recognition. *Biosystems Engineering*, 172, 84-91.

**[4]** <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>, <PERSON> (2001). *Manifesto for agile software development*. Retrieved from http://agilemanifesto.org/

**[5]** <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., ... & Amodei, D. (2020). Language models are few-shot learners. *Advances in neural information processing systems*, 33, 1877-1901.

**[6]** CAPMAS (2021). *Statistical Yearbook - Agriculture Sector*. Central Agency for Public Mobilization and Statistics, Egypt.

**[7]** Chollet, F. (2015). Keras. Retrieved from https://keras.io

**[8]** Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., ... & Houlsby, N. (2020). An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*.

**[9]** ECMA International (2021). *ECMAScript 2021 Language Specification*. Retrieved from https://www.ecma-international.org/

**[10]** El-Ramady, H., Alshaal, T., Bakr, N., Elbana, T., Mohamed, E., & Belal, A. A. (2019). Agriculture and food security in Egypt. *Springer*.

**[11]** Facebook Inc. (2023). *React - A JavaScript library for building user interfaces*. Retrieved from https://reactjs.org/

**[12]** FAO (2020). *The State of Food Security and Nutrition in the World 2020*. Food and Agriculture Organization of the United Nations, Rome.

**[13]** FAOSTAT (2023). *Food and Agriculture Data*. Food and Agriculture Organization of the United Nations. Retrieved from http://www.fao.org/faostat/

**[14]** Fielding, R. T. (2000). *Architectural styles and the design of network-based software architectures* (Doctoral dissertation, University of California, Irvine).

**[15]** Fowler, M. (2018). *Refactoring: improving the design of existing code*. Addison-Wesley Professional.

**[16]** Google (2021). *vit-base-patch16-224*. Hugging Face Model Hub. Retrieved from https://huggingface.co/google/vit-base-patch16-224

**[17]** Harris, C. R., Millman, K. J., Van Der Walt, S. J., Gommers, R., Virtanen, P., Cournapeau, D., ... & Oliphant, T. E. (2020). Array programming with NumPy. *Nature*, 585(7825), 357-362.

**[18]** Howard, A. G., Zhu, M., Chen, B., Kalenichenko, D., Wang, W., Weyand, T., ... & Adam, H. (2017). MobileNets: Efficient convolutional neural networks for mobile vision applications. *arXiv preprint arXiv:1704.04861*.

**[19]** Hughes, D., & Salathé, M. (2015). An open access repository of images on plant health to enable the development of mobile disease diagnostics. *arXiv preprint arXiv:1511.08060*.

**[20]** Hughes, D. P., & Salathé, M. (2015). *PlantVillage Dataset*. Retrieved from https://github.com/spMohanty/PlantVillage-Dataset

**[21]** Kaggle (2020). *Plant Disease Recognition Dataset*. Retrieved from https://www.kaggle.com/datasets/vipoooool/new-plant-diseases-dataset

**[22]** Kamilaris, A., & Prenafeta-Boldú, F. X. (2018). Deep learning in agriculture: A survey. *Computers and electronics in agriculture*, 147, 70-90.

**[23]** kerem (2022). *plant-village dataset*. Hugging Face Datasets. Retrieved from https://huggingface.co/datasets/kerem/plant-village

**[24]** linkanjarad (2023). *mobilenet_v2_1.0_224-plant-disease-identification*. Hugging Face Model Hub. Retrieved from https://huggingface.co/linkanjarad/mobilenet_v2_1.0_224-plant-disease-identification

**[25]** McKinney, W. (2010). Data structures for statistical computing in python. *Proceedings of the 9th Python in Science Conference*, 445, 51-56.

**[26]** Microsoft Corporation (2023). *.NET Core Documentation*. Retrieved from https://docs.microsoft.com/en-us/dotnet/core/

**[27]** Microsoft Corporation (2023). *Entity Framework Core Documentation*. Retrieved from https://docs.microsoft.com/en-us/ef/core/

**[28]** Ministry of Agriculture and Land Reclamation, Egypt (2021). *Agricultural Statistics Bulletin*. Cairo, Egypt.

**[29]** Mohanty, S. P., Hughes, D. P., & Salathé, M. (2016). Using deep learning for image-based plant disease detection. *Frontiers in plant science*, 7, 1419.

**[30]** mrSoul7766 (2023). *AgriQBot*. Hugging Face Model Hub. Retrieved from https://huggingface.co/mrSoul7766/AgriQBot

**[31]** Nielsen, J. (1994). *Usability engineering*. Morgan Kaufmann.

**[32]** Node.js Foundation (2023). *Node.js Runtime Environment*. Retrieved from https://nodejs.org/

**[33]** Norman, D. (2013). *The design of everyday things: Revised and expanded edition*. Basic books.

**[34]** OpenAPI Initiative (2023). *OpenAPI Specification*. Retrieved from https://swagger.io/specification/

**[35]** Pallets Projects (2023). *Flask Web Framework*. Retrieved from https://flask.palletsprojects.com/

**[36]** Paszke, A., Gross, S., Massa, F., Lerer, A., Bradbury, J., Chanan, G., ... & Chintala, S. (2019). PyTorch: An imperative style, high-performance deep learning library. *Advances in neural information processing systems*, 32.

**[37]** Pedregosa, F., Varoquaux, G., Gramfort, A., Michel, V., Thirion, B., Grisel, O., ... & Duchesnay, E. (2011). Scikit-learn: Machine learning in Python. *Journal of machine learning research*, 12, 2825-2830.

**[38]** Python Software Foundation (2023). *Python Programming Language*. Retrieved from https://www.python.org/

**[39]** Saleem, M. H., Potgieter, J., & Arif, K. M. (2019). Plant disease detection and classification by deep learning. *Plants*, 8(11), 468.

**[40]** Sandler, M., Howard, A., Zhu, M., Zhmoginov, A., & Chen, L. C. (2018). MobileNets: Efficient convolutional neural networks for mobile vision applications. *Proceedings of the IEEE conference on computer vision and pattern recognition*, 4510-4520.

**[41]** Sicnarf01 (2023). *agrigpt_expert*. Hugging Face Model Hub. Retrieved from https://huggingface.co/Sicnarf01/agrigpt_expert

**[42]** soma77 (2023). *agriculture_advisor*. Hugging Face Model Hub. Retrieved from https://huggingface.co/soma77/agriculture_advisor

**[43]** Tan, M., & Le, Q. (2019). EfficientNet: Rethinking model scaling for convolutional neural networks. *International conference on machine learning*, 6105-6114.

**[44]** Team, G., Anil, R., Borgeaud, S., Wu, Y., Alayrac, J. B., Yu, J., ... & Silver, D. (2023). Gemini: a family of highly capable multimodal models. *arXiv preprint arXiv:2312.11805*.

**[45]** UC Irvine Machine Learning Repository (2019). *Crop Recommendation Dataset*. Retrieved from https://archive.ics.uci.edu/ml/datasets/

**[46]** W3C (2021). *CSS3 Specification*. World Wide Web Consortium. Retrieved from https://www.w3.org/Style/CSS/

**[47]** W3C (2021). *HTML5 Specification*. World Wide Web Consortium. Retrieved from https://www.w3.org/TR/html52/

**[48]** Wolf, T., Debut, L., Sanh, V., Chaumond, J., Delangue, C., Moi, A., ... & Rush, A. M. (2020). Transformers: State-of-the-art natural language processing. *Proceedings of the 2020 conference on empirical methods in natural language processing: system demonstrations*, 38-45.

---

## **PLANNED PUBLICATIONS**

**[49]** [Author Name] (2025). AgroMind: A Comprehensive AI-Powered Agricultural Management System for Egyptian Farmers. *Journal of Agricultural Informatics* (In Preparation).

**[50]** [Author Name] (2025). Cultural Adaptation of AI Systems for Agricultural Applications in Developing Countries. *Computers and Electronics in Agriculture* (In Preparation).

**[51]** [Author Name] (2025). Mobile-Optimized Deep Learning for Plant Disease Detection in Resource-Constrained Environments. *IEEE Transactions on Agricultural Engineering* (In Preparation).

---

## **CONFERENCE PRESENTATIONS (PLANNED)**

- International Conference on Agricultural Engineering (ICAE 2025)
- IEEE International Conference on Computer Vision Applications (ICCVA 2025)
- ACM Conference on Computing for Sustainability (CompSust 2025)

---

*Total References: 51*

**Note:** This bibliography follows standard academic citation format suitable for graduation project documentation. All references are numbered sequentially and formatted according to academic standards.
