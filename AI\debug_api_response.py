#!/usr/bin/env python3
"""
Debug the API response to see what's being returned
"""

import requests
from PIL import Image
import io
import json

def create_test_image():
    """Create a simple test image"""
    img = Image.new('RGB', (224, 224), color='green')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def debug_api_response():
    """Debug the API response"""
    print("🔍 DEBUGGING API RESPONSE")
    print("=" * 50)
    
    try:
        test_image = create_test_image()
        files = {'image': ('test_leaf.jpg', test_image, 'image/jpeg')}
        
        print("📤 Sending request to disease detection API...")
        response = requests.post("http://127.0.0.1:5006/detect-disease", files=files)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 Full API Response:")
            print(json.dumps(result, indent=2))
            
            print(f"\n🔍 Key Fields Analysis:")
            print(f"   • message: '{result.get('message', 'NOT FOUND')}'")
            print(f"   • message length: {len(result.get('message', ''))}")
            print(f"   • advice: '{result.get('advice', 'NOT FOUND')[:100]}...'")
            print(f"   • advice length: {len(result.get('advice', ''))}")
            print(f"   • plant: '{result.get('plant', 'NOT FOUND')}'")
            print(f"   • disease: '{result.get('disease', 'NOT FOUND')}'")
            print(f"   • healthy: {result.get('healthy', 'NOT FOUND')}")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    debug_api_response()
