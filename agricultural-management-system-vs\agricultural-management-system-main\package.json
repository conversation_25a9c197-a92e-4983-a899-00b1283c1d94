{"name": "graduation-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "react-scripts start", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.5", "@reduxjs/toolkit": "^2.5.1", "animate.css": "^4.1.1", "aos": "^2.3.4", "axios": "^1.8.4", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "formik": "^2.4.6", "joi": "^17.13.3", "re-resizable": "^6.11.2", "react": "^18.3.1", "react-bootstrap": "^2.10.9", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.0.2", "yup": "^1.5.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "vite": "^6.0.1"}}