<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroMind - Professional Template Guide Style</title>
    <link
        href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;800&family=Open+Sans:wght@300;400;600;700&display=swap"
        rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%);
            color: #212529;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            position: absolute;
            top: 0;
            left: 0;
            background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%);
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide-header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 20px 40px;
            margin: -60px -60px 40px -60px;
            font-family: 'Montserrat', sans-serif;
            font-size: 48px;
            font-weight: 700;
            text-align: center;
            box-shadow: 0 4px 20px rgba(46, 125, 50, 0.3);
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 30px;
        }

        .title-slide {
            background: linear-gradient(rgba(46, 125, 50, 0.8), rgba(76, 175, 80, 0.8)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%234CAF50" width="1200" height="800"/><circle fill="%232E7D32" cx="200" cy="200" r="100" opacity="0.3"/><circle fill="%23FFC107" cx="1000" cy="600" r="150" opacity="0.2"/></svg>');
            background-size: cover;
            color: white;
            text-align: center;
            justify-content: center;
            align-items: center;
        }

        .title-slide .slide-header {
            background: none;
            margin: 0;
            font-size: 72px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .title-slide .subtitle {
            font-size: 36px;
            color: #FFC107;
            margin: 20px 0;
            font-weight: 600;
        }

        .title-slide .tagline {
            font-size: 24px;
            margin: 20px 0;
            font-weight: 300;
        }

        .title-slide .team-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: center;
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
        }

        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card .icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }

        .feature-card h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 24px;
            color: #2E7D32;
            margin-bottom: 15px;
        }

        .stats-box {
            background: linear-gradient(135deg, #263238 0%, #37474F 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .stats-box h3 {
            color: #FFC107;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            font-size: 18px;
        }

        .stat-item .icon {
            width: 30px;
            height: 30px;
            background: #FFC107;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #263238;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 20px;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
        }

        .comparison-table td {
            padding: 15px 20px;
            text-align: center;
            border-bottom: 1px solid #E0E0E0;
        }

        .comparison-table .agromind-col {
            background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
            font-weight: 600;
        }

        .bullet-list {
            list-style: none;
            padding: 0;
        }

        .bullet-list li {
            margin: 15px 0;
            padding-left: 40px;
            position: relative;
            font-size: 20px;
            line-height: 1.4;
        }

        .bullet-list li::before {
            content: '▶';
            position: absolute;
            left: 0;
            font-size: 16px;
            color: #2E7D32;
            font-weight: bold;
        }

        .success-list li::before {
            content: '✓';
            color: #4CAF50;
            font-weight: bold;
            font-size: 18px;
        }

        .warning-list li::before {
            content: '⚠';
            color: #FF9800;
            font-weight: bold;
            font-size: 18px;
        }

        .tech-list li::before {
            content: '●';
            color: #2196F3;
            font-weight: bold;
            font-size: 18px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
            border-left: 5px solid #FF9800;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 20px;
            font-weight: 600;
            text-align: center;
        }

        .infographic-bar {
            background: #E0E0E0;
            height: 30px;
            border-radius: 15px;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }

        .infographic-fill {
            height: 100%;
            background: linear-gradient(90deg, #2E7D32, #4CAF50);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: rgba(46, 125, 50, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1000;
        }

        .section-divider {
            background: linear-gradient(rgba(46, 125, 50, 0.9), rgba(76, 175, 80, 0.9)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%234CAF50" width="1200" height="800"/><polygon fill="%232E7D32" points="0,800 400,600 800,700 1200,500 1200,800" opacity="0.3"/></svg>');
            background-size: cover;
            color: white;
            text-align: center;
            justify-content: center;
            align-items: center;
        }

        .section-divider .slide-header {
            background: none;
            margin: 0;
            font-size: 64px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .section-divider .subtitle {
            font-size: 28px;
            color: #FFC107;
            margin-top: 20px;
            font-weight: 400;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin: 15px 0;
        }

        .metric-number {
            font-size: 3em;
            font-weight: bold;
            color: #2E7D32;
            display: block;
        }

        .metric-label {
            font-size: 1.1em;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide active title-slide">
            <div class="slide-content">
                <h1 class="slide-header">AGROMIND</h1>
                <p class="subtitle">Smart Agricultural Management System</p>
                <p class="tagline">Empowering Egyptian Farmers Through AI Technology</p>
                <div class="team-info">
                    <p><strong>Team Members:</strong> [Your Team Names]</p>
                    <p><strong>Supervisor:</strong> [Supervisor Name]</p>
                    <p><strong>University:</strong> [Your University]</p>
                    <p><strong>Date:</strong> [Presentation Date]</p>
                </div>
            </div>
        </div>

        <!-- Slide 2: Introduction -->
        <div class="slide">
            <div class="slide-header">What is AgroMind?</div>
            <div class="slide-content">
                <div class="three-column">
                    <div class="feature-card">
                        <div class="icon">ED</div>
                        <h3>EXPERT DASHBOARD</h3>
                        <ul class="bullet-list">
                            <li>Crop Management</li>
                            <li>Best Practices</li>
                            <li>Tools Database</li>
                            <li>Visual Guides</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="icon">AI</div>
                        <h3>AI POWERED</h3>
                        <ul class="bullet-list">
                            <li>Disease Detection</li>
                            <li>95.4% Accuracy</li>
                            <li>Instant Results</li>
                            <li>Arabic Support</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="icon">EC</div>
                        <h3>E-COMMERCE</h3>
                        <ul class="bullet-list">
                            <li>Product Marketplace</li>
                            <li>Secure Payments</li>
                            <li>Reviews System</li>
                            <li>Direct Trading</li>
                        </ul>
                    </div>
                </div>
                <div class="highlight-box">
                    Bridging Traditional Wisdom & Modern Technology
                </div>
            </div>
        </div>

        <!-- Slide 3: Problem Definition Section Divider -->
        <div class="slide section-divider">
            <div class="slide-content">
                <h1 class="slide-header">AGRICULTURAL CHALLENGES</h1>
                <p class="subtitle">Understanding the Need for Smart Solutions</p>
            </div>
        </div>

        <!-- Slide 4: Agricultural Challenges -->
        <div class="slide">
            <div class="slide-header">Current Challenges in Egyptian Agriculture</div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h3 style="color: #D32F2F; font-size: 28px; margin-bottom: 30px;">Current Challenges</h3>
                        <ul class="warning-list">
                            <li><strong>Limited Expert Access:</strong> Small-scale farmers lack agricultural
                                specialists</li>
                            <li><strong>Disease Identification:</strong> Difficulty in early crop disease detection</li>
                            <li><strong>Fragmented Information:</strong> Agricultural knowledge scattered across sources
                            </li>
                            <li><strong>Market Access Issues:</strong> Limited platforms for agricultural commerce</li>
                            <li><strong>Technology Gap:</strong> Traditional methods vs. modern digital solutions</li>
                            <li><strong>Economic Impact:</strong> Crop losses due to poor disease management</li>
                        </ul>
                    </div>
                    <div>
                        <div class="metric-card">
                            <span class="metric-number">25%</span>
                            <p class="metric-label">Workforce in Agriculture</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 25%;">25%</div>
                            </div>
                        </div>
                        <div class="metric-card">
                            <span class="metric-number">11%</span>
                            <p class="metric-label">GDP Contribution</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 11%;">11%</div>
                            </div>
                        </div>
                        <div class="metric-card">
                            <span class="metric-number">95%</span>
                            <p class="metric-label">Smartphone Usage</p>
                            <div class="infographic-bar">
                                <div class="infographic-fill" style="width: 95%;">95%</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="highlight-box">
                    Technology can bridge the knowledge gap between experts and farmers
                </div>
            </div>
        </div>

        <!-- Slide 5: Smart Solutions Need -->
        <div class="slide">
            <div class="slide-header">Why Smart Agricultural Management is Essential</div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h3 style="color: #2E7D32; font-size: 28px; margin-bottom: 30px;">Statistics & Facts</h3>
                        <ul class="success-list">
                            <li><strong>Egypt's Agriculture:</strong> 25% workforce, 11% GDP contribution</li>
                            <li><strong>Food Security:</strong> Growing population needs increased productivity</li>
                            <li><strong>AI Revolution:</strong> Technology can transform crop management</li>
                            <li><strong>Mobile Adoption:</strong> 95% of Egyptian farmers own smartphones</li>
                            <li><strong>Precision Agriculture:</strong> Data-driven decisions improve outcomes</li>
                            <li><strong>Knowledge Transfer:</strong> Bridge experts and farmers effectively</li>
                        </ul>
                    </div>
                    <div style="text-align: center;">
                        <div class="feature-card">
                            <div class="icon">GI</div>
                            <h3>GLOBAL IMPACT</h3>
                            <div class="metric-card">
                                <span class="metric-number">20-30%</span>
                                <p class="metric-label">Yield increase with technology adoption</p>
                                <div class="infographic-bar">
                                    <div class="infographic-fill" style="width: 75%;">Technology Impact</div>
                                </div>
                            </div>
                        </div>
                        <div class="feature-card" style="margin-top: 20px;">
                            <div class="icon">EG</div>
                            <h3>EGYPTIAN FOCUS</h3>
                            <p>Tailored for local agriculture and Arabic language support</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Existing Systems Section Divider -->
        <div class="slide section-divider">
            <div class="slide-content">
                <h1 class="slide-header">EXISTING SYSTEMS</h1>
                <p class="subtitle">Current Agricultural Management Platforms</p>
            </div>
        </div>

        <!-- Slide 7: FarmLogs Analysis -->
        <div class="slide">
            <div class="slide-header">FarmLogs - Field Management Platform</div>
            <div class="slide-content">
                <div class="two-column">
                    <div
                        style="background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%); padding: 30px; border-radius: 15px;">
                        <h3 style="color: #2E7D32; margin-bottom: 20px;">Strengths:</h3>
                        <ul class="success-list">
                            <li>Real-time weather forecasting</li>
                            <li>Field activity tracking and records</li>
                            <li>Crop health monitoring capabilities</li>
                            <li>Simple, user-friendly interface</li>
                        </ul>
                    </div>
                    <div
                        style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%); padding: 30px; border-radius: 15px;">
                        <h3 style="color: #F57C00; margin-bottom: 20px;">Limitations:</h3>
                        <ul class="warning-list">
                            <li>No AI-powered disease detection</li>
                            <li>No integrated e-commerce platform</li>
                            <li>Lacks expert consultation features</li>
                            <li>Primarily focused on US agriculture</li>
                        </ul>
                    </div>
                </div>
                <div class="highlight-box">
                    Common Gap: No AI disease detection or expert-farmer interaction
                </div>
            </div>
        </div>

        <!-- Slide 8: Competitive Analysis -->
        <div class="slide">
            <div class="slide-header">How AgroMind Stands Out</div>
            <div class="slide-content">
                <table class="comparison-table">
                    <tr>
                        <th>Feature</th>
                        <th>FarmLogs</th>
                        <th>Granular</th>
                        <th>AgriWebb</th>
                        <th class="agromind-col">AgroMind</th>
                    </tr>
                    <tr>
                        <td>AI Disease Detection</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td class="agromind-col">✓ 95.4% Accuracy</td>
                    </tr>
                    <tr>
                        <td>Expert Dashboard</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td class="agromind-col">✓ Full Management</td>
                    </tr>
                    <tr>
                        <td>E-commerce Platform</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td class="agromind-col">✓ Integrated</td>
                    </tr>
                    <tr>
                        <td>Arabic Language</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td class="agromind-col">✓ Bilingual</td>
                    </tr>
                    <tr>
                        <td>Egyptian Crop Focus</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td>✗</td>
                        <td class="agromind-col">✓ 14 Species</td>
                    </tr>
                    <tr>
                        <td>Mobile Optimized</td>
                        <td>✓</td>
                        <td>✗</td>
                        <td>✓</td>
                        <td class="agromind-col">✓ MobileNetV2</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Slide 9: Solution Overview Section Divider -->
        <div class="slide section-divider">
            <div class="slide-content">
                <h1 class="slide-header">OUR SOLUTION</h1>
                <p class="subtitle">Comprehensive Agricultural Management Platform</p>
            </div>
        </div>

        <!-- Slide 10: System Architecture -->
        <div class="slide">
            <div class="slide-header">AgroMind's Three-Pillar Architecture</div>
            <div class="slide-content">
                <div class="three-column">
                    <div class="feature-card">
                        <div class="icon">ED</div>
                        <h3>EXPERT DASHBOARD</h3>
                        <ul class="bullet-list">
                            <li>Agricultural specialists management</li>
                            <li>Crop data and best practices</li>
                            <li>Tools and resources database</li>
                            <li>Regional coverage mapping</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="icon">FD</div>
                        <h3>FARMER DASHBOARD</h3>
                        <ul class="bullet-list">
                            <li>Personalized recommendations</li>
                            <li>AI disease detection</li>
                            <li>Stage-specific guidance</li>
                            <li>Arabic language support</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="icon">EC</div>
                        <h3>E-COMMERCE PLATFORM</h3>
                        <ul class="bullet-list">
                            <li>Integrated marketplace</li>
                            <li>Smart product recommendations</li>
                            <li>Secure payment processing</li>
                            <li>Quality assurance system</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← Previous</button>
        <button class="nav-btn" onclick="nextSlide()">Next →</button>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">20</span>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function (e) {
            if (e.key === 'ArrowRight' || e.key === ' ') nextSlide();
            if (e.key === 'ArrowLeft') previousSlide();
        });
    </script>
</body>

</html>